<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Product_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-product_name" data-rule="required" class="form-control" name="row[product_name]" type="text" value="{$row.product_name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Subsystem_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-subsystem_id" min="0" data-rule="required" class="form-control" name="row[subsystem_id]" type="text" value="{$row.subsystem_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Platform_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-platform_id" min="0" data-rule="required" data-source="system/platform/index" class="form-control selectpage" name="row[platform_id]" type="text" value="{$row.platform_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Customer_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-customer_id" min="0" data-rule="required" data-source="system/customer/index" class="form-control selectpage" name="row[customer_id]" type="text" value="{$row.customer_id|htmlentities}" data-field="customer_name" data-primary-key="customer_id">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
