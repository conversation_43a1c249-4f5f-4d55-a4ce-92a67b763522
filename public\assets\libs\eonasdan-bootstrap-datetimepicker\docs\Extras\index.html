﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    
    
    <link rel="apple-touch-icon" sizes="57x57" href="/apple-touch-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="114x114" href="/apple-touch-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="72x72" href="/apple-touch-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="144x144" href="/apple-touch-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="60x60" href="/apple-touch-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="120x120" href="/apple-touch-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="76x76" href="/apple-touch-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="152x152" href="/apple-touch-icon-152x152.png">
    <link rel="icon" type="image/png" href="/favicon-196x196.png" sizes="196x196">
    <link rel="icon" type="image/png" href="/favicon-160x160.png" sizes="160x160">
    <link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96">
    <link rel="icon" type="image/png" href="/favicon-16x16.png" sizes="16x16">
    <link rel="icon" type="image/png" href="/favicon-32x32.png" sizes="32x32">
    <meta name="msapplication-TileColor" content="#2b5797">
    <meta name="msapplication-TileImage" content="/mstile-144x144.png">

    <title></title>

    <link rel="stylesheet" type="text/css" media="screen"
          href="//maxcdn.bootstrapcdn.com/bootstrap/3.3.1/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="//maxcdn.bootstrapcdn.com/font-awesome/4.3.0/css/font-awesome.min.css">
    <link href="../css/prettify-1.0.css" rel="stylesheet">
    <link href="../css/base.css" rel="stylesheet">
    <link href="//cdn.rawgit.com/Eonasdan/bootstrap-datetimepicker/e8bddc60e73c1ec2475f827be36e1957af72e2ea/build/css/bootstrap-datetimepicker.css" rel="stylesheet">

    <script type="text/javascript" src="//code.jquery.com/jquery-2.1.1.min.js"></script>
    <script type="text/javascript" src="//maxcdn.bootstrapcdn.com/bootstrap/3.3.1/js/bootstrap.min.js"></script>
    
    <script src="//cdnjs.cloudflare.com/ajax/libs/moment.js/2.9.0/moment-with-locales.js"></script>
    
    
    <script src="//cdn.rawgit.com/Eonasdan/bootstrap-datetimepicker/e8bddc60e73c1ec2475f827be36e1957af72e2ea/src/js/bootstrap-datetimepicker.js"></script>
    
    
    
</head>

<body>

<div class="navbar navbar-default navbar-fixed-top" role="navigation">
    <div class="container">

        <!-- Collapsed navigation -->
        <div class="navbar-header">
            <!-- Expander button -->
            <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>

            <!-- Main title -->
            <a class="navbar-brand" href=""></a>
        </div>

        <!-- Expanded navigation -->
        <div class="navbar-collapse collapse">
            <!-- Main navigation -->
            <ul class="nav navbar-nav">
            
            
                <li >
                    <a href="..">Usage</a>
                </li>
            
            
            
                <li >
                    <a href="../Installing/">Installing</a>
                </li>
            
            
            
                <li >
                    <a href="../Functions/">Functions</a>
                </li>
            
            
            
                <li >
                    <a href="../Options/">Options</a>
                </li>
            
            
            
                <li >
                    <a href="../Events/">Events</a>
                </li>
            
            
            
                <li >
                    <a href="../Changelog.md">Change Log</a>
                </li>
            
            
            
                <li >
                    <a href="../ContributorsGuide/">Dev Guide</a>
                </li>
            
            
            
                <li class="active">
                    <a href="./">Extras</a>
                </li>
            
            
            
                <li >
                    <a href="../FAQ/">FAQs</a>
                </li>
            
            
            </ul>

            <!-- Search, Navigation and Repo links -->
            <ul class="nav navbar-nav navbar-right">
                
				
                
            </ul>
        </div>
    </div>
</div>

<div class="container">
    <div class="row">
        <div class="col-md-3"><script async type="text/javascript" src="//cdn.carbonads.com/carbon.js?serve=CK7DC5QN&placement=eonasdangithubio" id="_carbonads_js"></script>
<div class="bs-sidebar hidden-print affix well" role="complementary">    <ul class="nav bs-sidenav">
    
        <li class="main active"><a href="#extras">Extras</a></li>
        
            <li><a href="#rails-3">Rails 3</a></li>
        
            <li><a href="#ie-7">IE 7</a></li>
        
    
    </ul>
</div></div>
        <div class="col-md-8" role="main">
            <div class="alert alert-danger" style="font-size:1.5em;">
                <strong>Important!</strong>
                Please read this <a href="https://eonasdan.com/posts/state-of-my-picker" target="_blank">blog post</a>
            </div>
            

<h1 id="extras">Extras</h1>
<p>Guides for making the picker work better with rails, IE, etc</p>
<h2 id="rails-3">Rails 3</h2>
<p>by <a href="https://github.com/dhulihan">dhulihan</a></p>
<p>You can easily override the default rails form helpers (<code>date_select</code> and <code>datetime_select</code>) with bootstrap-datetimepicker for a much nicer experience. </p>
<pre><code class="rb"># Add to config/initializers/form.rb or the end of app/helpers/application_helper.rb
module ActionView
  module Helpers
    class FormBuilder 
      def date_select(method, options = {}, html_options = {})
        existing_date = @object.send(method) 
        formatted_date = existing_date.to_date.strftime(&quot;%F&quot;) if existing_date.present?
        @template.content_tag(:div, :class =&gt; &quot;input-group&quot;) do    
          text_field(method, :value =&gt; formatted_date, :class =&gt; &quot;form-control datepicker&quot;, :&quot;data-date-format&quot; =&gt; &quot;YYYY-MM-DD&quot;) +
          @template.content_tag(:span, @template.content_tag(:span, &quot;&quot;, :class =&gt; &quot;glyphicon glyphicon-calendar&quot;) ,:class =&gt; &quot;input-group-addon&quot;)
        end
      end

      def datetime_select(method, options = {}, html_options = {})
        existing_time = @object.send(method) 
        formatted_time = existing_time.to_time.strftime(&quot;%F %I:%M %p&quot;) if existing_time.present?
        @template.content_tag(:div, :class =&gt; &quot;input-group&quot;) do    
          text_field(method, :value =&gt; formatted_time, :class =&gt; &quot;form-control datetimepicker&quot;, :&quot;data-date-format&quot; =&gt; &quot;YYYY-MM-DD hh:mm A&quot;) +
          @template.content_tag(:span, @template.content_tag(:span, &quot;&quot;, :class =&gt; &quot;glyphicon glyphicon-calendar&quot;) ,:class =&gt; &quot;input-group-addon&quot;)
        end
      end
    end
  end
end
</code></pre>

<p>The time format used here is ActiveRecord-friendly, which means it will be parsed correctly when passed in through <code>params</code> to your record.</p>
<p>That's all there is to it! Now all of your forms that use <code>datetime_select</code> or <code>date_select</code> will be automatically updated:</p>
<pre><code class="erb">&lt;% form_for @post do |f| %&gt;
    &lt;div class=&quot;form-group&quot;&gt;
        &lt;label&gt;Published At&lt;/label&gt;
        &lt;%= f.datetime_select :published_at %&gt;
    &lt;/div&gt;
&lt;% end %&gt;
</code></pre>

<h2 id="ie-7">IE 7</h2>
<p>by <a href="https://github.com/EquilibriumCST">EquilibriumCST</a></p>
<p>I succeed to run this widget under IE7.
Here is what I did.</p>
<ol>
<li>
<p>gliphicons are not working under IE7 so add <a href="https://github.com/coliff/bootstrap-ie7">this css file</a>. And this enables the icons.</p>
</li>
<li>
<p>Z-index problem with IE 7. I added position: relative and <code>z-index: 10</code> to the parent container. Otherwise popup is shown under the next elements.</p>
</li>
<li>
<p>JS events were not working well. </p>
</li>
</ol>
<p>If you open the datetimepicker widget and click on some button or date inside it, widget is automatically closed.
So I added <code>debug: true</code> as an option when initializing the widget. Why I did this? I saw on line 1121 from bootsrap-datetimepicker.js the code <code>'blur': options.debug ? '' : hide</code>. 
And now widget window is not closed on every click inside it, but now you can't close it anyway :) 
And closing should be done manually. I've added this document click handler. If you click something outside the widget, now closing works.</p>
<pre><code>$(document).click(function(e){
            var target = $(e.target);
            if(target.parents('.bootstrap-datetimepicker-widget').length &lt; 1 &amp;&amp; !target.hasClass('datetimepickerInput') &amp;&amp; !target.hasClass('datepickerIcon') &amp;&amp; !target.hasClass('clockpickerIcon')){
                if($('.bootstrap-datetimepicker-widget').length &gt; 0){
                    $('#startDate').data('DateTimePicker').hide();
                    $('#startTime').data('DateTimePicker').hide();
                    $('.datetimepickerInput').blur();
                }
            }
        });
</code></pre>

<p>But if you have more than one widget on the page like I did, clicking on one widget does'n close the other. Added below lines and now all works fine.</p>
<pre><code>$('#widget1').on(&quot;dp.show&quot;,function (e) {
    $('#widget2).data('DateTimePicker').hide();
});

$('#widget2').on(&quot;dp.show&quot;,function (e) {
    $('#widget1).data('DateTimePicker').hide();
});
</code></pre>

<p>I hope this will help to the others who are fighting with the old IE versions :)</p>
        </div>
    </div>
</div>



<script src="../js/prettify-1.0.min.js"></script>
<script src="../js/base.js"></script>
<script>
    if (top != self) {
        top.location.replace(self.location.href);
    }
    (function (i, s, o, g, r, a, m) {
        i['GoogleAnalyticsObject'] = r;
        i[r] = i[r] || function () {
            (i[r].q = i[r].q || []).push(arguments)
        }, i[r].l = 1 * new Date();
        a = s.createElement(o),
            m = s.getElementsByTagName(o)[0];
        a.async = 1;
        a.src = g;
        m.parentNode.insertBefore(a, m)
    })(window, document, 'script', '//www.google-analytics.com/analytics.js', 'ga');

    ga('create', 'UA-47462200-1', 'eonasdan.github.io');
    ga('send', 'pageview');
</script>
</body>
</html>