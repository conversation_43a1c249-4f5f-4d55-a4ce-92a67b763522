<?php

namespace app\api\controller\unified;

use app\common\controller\Api;

use think\Db;

class Index extends Api
{

	protected $noNeedLogin = '*';
    protected $noNeedRight = '*';



    /**
     * 获取基础信息
     */
	public function getBasic()
	{

		$id = input('id');
	
		if (empty($id)) {
			$this->error('参数错误');
		}

		$row = Db::name('unified_product')->where('id',$id)
		// ->field('id,name,logo,school_id,first_title,second_title,course_title,course_tag,course_description,course_hours,module_one_name,module_two_name,module_three_name')
		->find();
		$school_id = $row['school_id'];
    	$subject_id = Db::name('eva_subject')->where('school_id',$school_id)->column('id');

    	$where = [];
    	$where['subject_id'] = ['in',$subject_id];
    	$row['logo'] = cdnurl($row['logo'],true);
    	$row['course_image'] = cdnurl($row['course_image'],true);
    	$row['course_tag'] = explode(',',$row['course_tag']);
    	$row['school_name'] = Db::name('org_school')->where('id',$school_id)->value('name');

    	$data = [];
    	$data['material_num'] = Db::name('basic_material')
    	->where('cognitive_type_tag','<>','')
    	->where('is_political',0)
    	->where($where)
    	->count();
    	$data['knowledge_num'] = Db::name('basic_knowledge')->where($where)->where('is_political',0)->count();
    	$data['task_num'] = Db::name('basic_task')->where($where)->where('is_political',0)->count();
    	$data['ability_num'] = Db::name('basic_ability')->where($where)->where('is_political',0)->count();
    	$data['political_material'] = Db::name('basic_material')->where($where)->where('is_political',1)->count();
    	$data['political_knowledge'] = Db::name('basic_knowledge')->where($where)->where('is_political',1)->count();
    	$data['political_task'] = Db::name('basic_task')->where($where)->where('is_political',1)->count();
    	$data['political_ability'] = Db::name('basic_ability')->where($where)->where('is_political',1)->count();
    	$data['political_num'] = $data['political_material'] + $data['political_knowledge'] + $data['political_task'] + $data['political_ability'];

    	$row['statistics_data'] = $data;

    	$this->success('success',$row);
	}



	/**
	* 获取产品列表
	*/
	public function getProducts()
	{

		$id = input('id');
		if (empty($id)) {
			$this->error('参数错误');
		}

		$list = Db::name('unified_product_list')->where('page_id',$id)->order('weigh desc')->select();
		
		$module1 = $module2 = $module3 = [];
		foreach ($list as &$v) {
			$v['image'] = cdnurl($v['image'],true);

			if ($v['module'] == 1) {
				$module1[] = $v;
			} else if ($v['module'] == 2) {
				$module2[] = $v;
			} else if ($v['module'] == 3) {
				$module3[] = $v;
			}

		}


		$this->success('success',['module1' => $module1,'module2' => $module2,'module3' => $module3]);

	}



	/*
	* 获取知识点列表 
	*/
	public function getKnowledgeList()
	{	

		$id = input('id');
	
		if (empty($id)) {
			$this->error('参数错误');
		}

		$row = Db::name('unified_product')->where('id',$id)->field('school_id')->find();

		$school_id = $row['school_id']; 
    	$subject_id = Db::name('eva_subject')->where('school_id',$school_id)->column('id');

    	$where = [];
    	$where['subject_id'] = ['in',$subject_id];


		$depth_tag = ['记忆','理解','应用','分析','评价','创造'];
		$type_tag = ['事实性知识','概念性知识','程序性知识','元认知知识','技能点'];
		$list = Db::name('basic_knowledge')
		->where('is_political',0)
		->where($where)
		->group('cognitive_type_tag,cognitive_depth_tag')
		->field('count(*) as count, cognitive_type_tag,cognitive_depth_tag')
		->select();

		// 组织数据结构
		$matrix = [];
		foreach ($list as $item) {
		    $types = explode(',', $item['cognitive_type_tag']);
		    $depths = explode(',', $item['cognitive_depth_tag']);
		    foreach ($types as $type) {
		        $type = trim($type);
		        foreach ($depths as $depth) {
		            $depth = trim($depth);
		            if (empty($depth)) continue;
		            if (!isset($matrix[$type])) {
		                $matrix[$type] = array_fill_keys($depth_tag, 0);
		            }
		            // 确保深度标签在预定义顺序中
		            if (in_array($depth, $depth_tag)) {
		                $matrix[$type][$depth] += $item['count'];
		            }
		        }
		    }
		}

		$body = [];
		foreach ($type_tag as $t) {
			$tmp = [];
			$tmp[] = $t;
			foreach ($matrix as $key => $val) {
				if ($key == $t) {
					foreach ($val as $k => $v) {
						$tmp[] = $v;
						continue 1;
					}
				}
			}
			$body[] = $tmp;
		}

		$this->success('success',['head' => $depth_tag,'body' => $body]);

	}


	/**
	 * 能力雷达 
	**/
	public function getAbilityRadar()
	{
		$id = input('id');
	
		if (empty($id)) {
			$this->error('参数错误');
		}

		$row = Db::name('unified_product')->where('id',$id)->field('school_id')->find();

		$school_id = $row['school_id']; 
    	$subject_id = Db::name('eva_subject')->where('school_id',$school_id)->column('id');

    	$typeArr = ["安全与规范意识","技术操作能力","创新与改进能力","认知与分析能力","思政能力"];

    	$where = [];
    	$where['subject_id'] = ['in',$subject_id];

		$list = Db::name('basic_ability')
		// ->where('is_political',0)
		->where($where)
		->group('type_tag')
		->field('count(*) as count,type_tag')
		->order('count desc')
		->select();

		foreach ($list as &$v) {
			$v['max'] = round($v['count'] + $v['count'] * 0.2);
		}
		unset($v);

		$existType = array_column($list,'type_tag');
		foreach ($typeArr as $v) {
			if (!in_array($v,$existType)) {
				$list[] = [
					'type_tag' => $v,
					'count' => 0,
					'max' => 0
				];
			}
		}
		$this->success('success',$list);
	}


	/*
	* 获取任务占比
	*/
	public function getTaskPercentage()
	{

		$id = input('id');
	
		if (empty($id)) {
			$this->error('参数错误');
		}

		$row = Db::name('unified_product')->where('id',$id)->field('school_id')->find();

		$school_id = $row['school_id']; 
    	$subject_id = Db::name('eva_subject')->where('school_id',$school_id)->column('id');


    	$where = [];
    	$where['subject_id'] = ['in',$subject_id];
    	$list = Db::name('basic_task')
		->where('is_political',0)
		->where($where)
		->group('type_tag')
		->field('count(*) as count, type_tag')
		->select();

    	$hash = [];
		foreach ($list as $v) {
			$tags = explode(',',$v['type_tag']);
			foreach ($tags as $tag) {
				$tag = trim($tag);
				if (isset($hash[$tag])) {
					$hash[$tag] += $v['count'];
				} else {
					$hash[$tag] = $v['count'];
				}
			}
		}	

    	$typeArr = ['理论认知','实践操作','问题解决','综合设计','创新探究'];
		$existType = array_column($list,'type_tag');
		foreach ($typeArr as $v) {
			if (!in_array($v,$existType)) {
				$hash[$v] = 0;
			}
		}

		$total = array_sum($hash);
		$result = [];
		foreach ($hash as $k => $v) {
			$rate = round($v / $total * 100);
			$result[] = [
				'name' => $k,
				'value' => $rate
			];
		}

		$this->success('success',$result);
	}





	/**
	 * 获取资源列表
	*/
	public function getMaterialList()
	{
		$id = input('id');
	
		if (empty($id)) {
			$this->error('参数错误');
		}

		$row = Db::name('unified_product')->where('id',$id)->field('school_id')->find();

		$school_id = $row['school_id']; 
    	$subject_id = Db::name('eva_subject')->where('school_id',$school_id)->column('id');

    	$where = [];
    	$where['subject_id'] = ['in',$subject_id];

    	$list = Db::name('basic_material')
		->where('is_political',0)
		->where('cognitive_type_tag','<>','')
		->where($where)
		->group('cognitive_type_tag')
		->field('count(*) as count, cognitive_type_tag')
		->select();


		$hash = [];
		foreach ($list as $v) {
			$tags = explode(',',$v['cognitive_type_tag']);
			foreach ($tags as $tag) {
				if (isset($hash[$tag])) {
					$hash[$tag] += $v['count'];
				} else {
					$hash[$tag] = $v['count'];
				}
			}
		}

    	$typeArr = ['元认知知识','事实性知识','程序性知识','概念性知识','技能点','知识点'];
		$existType = array_column($list,'cognitive_type_tag');
		foreach ($typeArr as $v) {
			if (!in_array($v,$existType)) {
				$hash[$v] = 0;
			}
		}

		$result = [];
		foreach ($hash as $k => $v) {
			$result[] = [
				'name' => $k,
				'value' => $v
			];
		}

		$this->success('success',$result);


	}



}