{"name": "dragsort", "version": "1.0.4", "authors": ["<PERSON>"], "description": "A javascript file that provides the ability to sort lists using drag and drop.", "main": "jquery.dragsort.js", "keywords": ["javascript", "sort", "j<PERSON>y"], "license": "http://dragsort.codeplex.com/license", "homepage": "http://dragsort.codeplex.com/", "ignore": ["**/.*", "bower_components", "examples"], "dependencies": {"jquery": "~3.7.0"}, "_release": "1.0.4", "_resolution": {"type": "version", "tag": "v1.0.4", "commit": "972b050fdc45f84dc3d0eadf91f209832082fb14"}, "_source": "https://github.com/karsonzhang/fastadmin-dragsort.git", "_target": "~1.0.0", "_originalSource": "fastadmin-dragsort"}