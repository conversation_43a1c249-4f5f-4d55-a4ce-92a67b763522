<?php

namespace app\admin\model\system;

use think\Model;
use traits\model\SoftDelete;
use think\Db;

class AuthProject extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'auth_project';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'status_text'
    ];
    

    
    public function getStatusList()
    {
        return ['normal' => __('Normal'), 'closed' => __('Hidden')];
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }




    public function customer()
    {
        return $this->belongsTo('app\admin\model\Customer', 'customer_id', 'customer_id', [], 'LEFT')->setEagerlyType(0);
    }


    public function admin()
    {
        return $this->belongsTo('app\admin\model\Admin', 'admin_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
    

    /**
     * 同步授权信息到子系统数据库
     * @param $auth_id int 授权项目ID
     * @return array 同步结果
     * @throws \Exception
     */
    public function syncAuthorization($auth_id){
        try {
            // 记录开始日志
            \think\Log::info("开始同步授权信息到子系统", ['auth_id' => $auth_id]);

            // 参数验证
            if (empty($auth_id) || !is_numeric($auth_id)) {
                throw new \Exception("授权项目ID参数无效: {$auth_id}");
            }

            // 获取客户ID
            $customer_id = \app\admin\model\system\AuthProject::where('auth_id', $auth_id)->value('customer_id');
            if (empty($customer_id)) {
                throw new \Exception("未找到授权项目或客户信息: auth_id={$auth_id}");
            }

            // 获取出货单产品列表
            $outboundList = Db::name('auth_outbound')->where('auth_id', $auth_id)->select();
            if (empty($outboundList)) {
                \think\Log::warning("未找到出货单信息", ['auth_id' => $auth_id]);
                return [];
            }

            $outboundProduct = [];
            foreach($outboundList as $outbound){
                $outboundProductRow = Db::name('outbound_product')->where('outbound_id', $outbound['outbound_id'])->column('product_id');
                $outboundProduct = array_merge($outboundProduct, $outboundProductRow);
            }

            if (empty($outboundProduct)) {
                \think\Log::warning("未找到出货产品信息", ['auth_id' => $auth_id]);
                return [];
            }

            // 获取产品信息
            $product = \app\admin\model\system\Product::where('product_id', 'in', $outboundProduct)->select();
            if (empty($product)) {
                throw new \Exception("未找到产品信息");
            }

            // 初始化计数器
            $syncResults = [];
            $ai_insert_num = $vtrs_insert_num = $formmaster_insert_num = $examadmin_insert_num = $case_insert_num = 0;

            // 获取客户名称
            $customer_name = \app\admin\model\Customer::where('customer_id', $customer_id)->value('customer_name');
            if (empty($customer_name)) {
                throw new \Exception("未找到客户名称信息: customer_id={$customer_id}");
            }

            foreach($product as $item){
                try {
                    // 获取授权信息
                    $auth = Db::name('auth_project_authorization')->where(['auth_id'=>$auth_id,'product_id'=>$item->product_id])->find();

                    // 构建用户查询条件
                    $where = [];
                    if($auth && $auth['visibility'] == 2){
                        if (empty($auth['user_ids'])) {
                            \think\Log::warning("授权用户ID为空", ['auth_id' => $auth_id, 'product_id' => $item->product_id]);
                            continue;
                        }
                        $where = [
                            'id'=>['in', explode(',', $auth['user_ids'])],
                            'customer_id' => $customer_id,
                        ];
                    }else{
                        $where = [
                            'customer_id' => $customer_id,
                        ];
                    }

                    // 获取授权用户
                    $userArr = \app\admin\model\User::where($where)->select();
                    if (empty($userArr)) {
                        \think\Log::warning("未找到授权用户", ['auth_id' => $auth_id, 'product_id' => $item->product_id, 'where' => $where]);
                        continue;
                    }

                    // 获取平台信息
                    $platform = \app\admin\model\system\Platform::where('id', $item->platform_id)->find();
                    if (empty($platform)) {
                        \think\Log::error("未找到平台信息", ['platform_id' => $item->platform_id, 'product_id' => $item->product_id]);
                        continue;
                    }

                    \think\Log::info("开始同步产品到平台", [
                        'product_id' => $item->product_id,
                        'platform' => $platform->config_name,
                        'user_count' => count($userArr)
                    ]);

                    // 根据平台类型进行同步
                    switch ($platform->config_name) {
                        case 'aimaster':
                            try {
                                $query = Db::connect('aimaster');
                                $insert = [];
                                foreach($userArr as $user) {
                                    try {
                                        $group_id = $user->group_id == 2 ? 1 : 2;
                                        $row = $query->name('user')->where(['project_id' => $item->subsystem_id,'username' => $user->username])->find();
                                        $userInsert = [
                                            'group_id' => $group_id,
                                            'project_id' => $item->subsystem_id,
                                            'username' => $user->username,
                                            'nickname' => $user->nickname,
                                            'password' => $user->password,
                                            'salt' => $user->salt,
                                            'mobile' => $user->mobile,
                                            'avatar' => $user->avatar,
                                            'createtime' => $user->createtime,
                                            'status' => $user->status,
                                        ];
                                        if(!$row) {
                                            $insert[] = $userInsert;
                                            $ai_insert_num++;
                                        } else {
                                            \think\Log::info("用户已存在，跳过插入", ['username' => $user->username, 'platform' => 'aimaster']);
                                            continue;
                                        }
                                    } catch (\Exception $e) {
                                        \think\Log::error("aimaster用户处理失败", ['username' => $user->username, 'error' => $e->getMessage()]);
                                        continue;
                                    }
                                }

                                if($insert) {
                                    $query->name('user')->insertAll($insert);
                                    \think\Log::info("aimaster批量插入用户成功", ['count' => count($insert)]);
                                }

                                $syncResults[] = [
                                    'platform' => $platform->name,
                                    'num' => $ai_insert_num,
                                ];
                            } catch (\Exception $e) {
                                \think\Log::error("aimaster平台同步失败", ['error' => $e->getMessage(), 'product_id' => $item->product_id]);
                                $syncResults[] = [
                                    'platform' => $platform->name,
                                    'num' => 0,
                                    'error' => $e->getMessage()
                                ];
                            }
                            break;
                        case 'vtrs':
                            try {
                                $query = Db::connect('vtrs');
                                $time = time();
                                foreach($userArr as $user) {
                                    try {
                                        $insert = [];
                                        $group_id = $user->group_id == 2 ? 2 : 1;
                                        $row = $query->name('user')->where(['username' => $user->username])->find();
                                        $userInsert = [
                                            'group_id' => $group_id,
                                            'username' => $user->username,
                                            'nickname' => $user->nickname,
                                            'password' => $user->password,
                                            'salt' => $user->salt,
                                            'mobile' => $user->mobile,
                                            'avatar' => $user->avatar,
                                            'createtime' => $user->createtime,
                                            'status' => $user->status,
                                        ];

                                        $insertId = null;
                                        if(!$row) {
                                            $insertId = $query->name('user')->insertGetId($userInsert);
                                            $vtrs_insert_num++;
                                            \think\Log::info("vtrs用户插入成功", ['username' => $user->username, 'user_id' => $insertId]);
                                        } else {
                                            $insertId = $row['id'];
                                            \think\Log::info("vtrs用户已存在", ['username' => $user->username, 'user_id' => $insertId]);
                                        }

                                        $proId = $classId = $gradeId = $schoolId = null;
                                        $schoolId = $query->name('user_school')->where(['name' => $customer_name])->value('id');
                                        if (!$schoolId) {
                                            \think\Log::warning("vtrs未找到学校信息", ['customer_name' => $customer_name]);
                                            continue;
                                        }
                                        $productIds = $item->subsystem_id;

                                        # 学生获得班级等信息
                                        if($group_id != 2) {
                                            # 获取用户组织信息
                                            $org = \app\admin\model\system\Org::where('org_id', $user->org_id)->find();
                                            if(!empty($org)) {
                                                $org1 = \app\admin\model\system\Org::where('org_id', $org->parent_id)->find();
                                                $org2 = \app\admin\model\system\Org::where('org_id', $org1->parent_id)->find();

                                                # 用户专业
                                                $proId = $query->name('user_profession')->where(['school_id'=>$schoolId,'name' => $org2['org_name']])->value('id');
                                                if(!$proId) {
                                                    $proId = $query->name('user_profession')->insertGetId(['school_id'=>$schoolId,'name' => $org2['org_name'],'status'=>1,'createtime'=>$time]);
                                                }
                                                # 用户年级
                                                $gradeId = $query->name('user_grade')->where(['pro_id'=> $proId,'name' => $org1['org_name']])->value('id');
                                                if(!$gradeId) {
                                                    $gradeId = $query->name('user_grade')->insertGetId(['pro_id'=>$proId,'name' => $org1['org_name'],'status'=>1,'createtime'=>$time]);
                                                }

                                                # 用户班级
                                                $classId = $query->name('user_classes')->where(['grade_id'=>$gradeId,'name' => $org['org_name']])->value('id');
                                                if(!$classId) {
                                                    $classId = $query->name('user_classes')->insertGetId(['grade_id'=>$gradeId,'pro_id'=>$proId,'school_id'=>$schoolId,'name' => $org['org_name'],'status'=>1,'createtime'=>$time]);
                                                }
                                            }
                                        }

                                        # 用户扩展表
                                        $userProductRow = $query->name('user_product')->where(['user_id' => $insertId])->find();
                                        if($userProductRow) {
                                            $update = [
                                                'pro_id' => $proId,
                                                'class_id' => $classId,
                                                'grade_id' => $gradeId,
                                                'school_id' => $schoolId,
                                                'product_ids' => $productIds,
                                                'updatetime' => time(),
                                            ];
                                            $query->name('user_product')->where(['user_id' => $insertId])->update($update);
                                        } else {
                                            $insertData = [
                                                'user_id' => $insertId,
                                                'pro_id' => $proId,
                                                'class_id' => $classId,
                                                'grade_id' => $gradeId,
                                                'school_id' => $schoolId,
                                                'product_ids' => $productIds,
                                                'createtime' => time(),
                                            ];
                                            $query->name('user_product')->insert($insertData);
                                        }
                                    } catch (\Exception $e) {
                                        \think\Log::error("vtrs用户处理失败", ['username' => $user->username, 'error' => $e->getMessage()]);
                                        continue;
                                    }
                                }

                                $syncResults[] = [
                                    'platform' => $platform->name,
                                    'num' => $vtrs_insert_num,
                                ];
                            } catch (\Exception $e) {
                                \think\Log::error("vtrs平台同步失败", ['error' => $e->getMessage(), 'product_id' => $item->product_id]);
                                $syncResults[] = [
                                    'platform' => $platform->name,
                                    'num' => 0,
                                    'error' => $e->getMessage()
                                ];
                            }
                            break;
                        case 'formmaster':
                            try {
                                $query = Db::connect('formmaster');
                                $time = time();
                                foreach($userArr as $user) {
                                    try {
                                        $insert = [];
                                        $group_id = $user->group_id == 2 ? 2 : 1;
                                        $row = $query->name('user')->where(['username' => $user->username])->find();

                                        $proId = $classId = $gradeId = $schoolId = null;
                                        $schoolId = $query->name('school')->where(['name' => $customer_name])->value('id');
                                        if (!$schoolId) {
                                            \think\Log::warning("formmaster未找到学校信息", ['customer_name' => $customer_name]);
                                            continue;
                                        }

                                        # 学生获得班级等信息
                                        if($group_id != 2) {
                                            # 获取用户组织信息
                                            $org = \app\admin\model\system\Org::where('org_id', $user->org_id)->find();
                                            if(!empty($org)) {
                                                $org1 = \app\admin\model\system\Org::where('org_id', $org->parent_id)->find();
                                                $org2 = \app\admin\model\system\Org::where('org_id', $org1->parent_id)->find();

                                                # 用户专业
                                                $proId = $query->name('profession')->where(['school_id'=>$schoolId,'name' => $org2['org_name']])->value('id');
                                                if(!$proId) {
                                                    $proId = $query->name('profession')->insertGetId(['school_id'=>$schoolId,'name' => $org2['org_name'],'createtime'=>$time]);
                                                }
                                                # 用户年级
                                                $gradeId = $query->name('grade')->where(['profession_id'=> $proId,'name' => $org1['org_name']])->value('id');
                                                if(!$gradeId) {
                                                    $gradeId = $query->name('grade')->insertGetId(['profession_id'=>$proId,'name' => $org1['org_name'],'createtime'=>$time]);
                                                }

                                                # 用户班级
                                                $classId = $query->name('classes')->where(['grade_id'=>$gradeId,'name' => $org['org_name']])->value('id');
                                                if(!$classId) {
                                                    $classId = $query->name('classes')->insertGetId(['grade_id'=>$gradeId,'pro_id'=>$proId,'school_id'=>$schoolId,'name' => $org['org_name'],'createtime'=>$time]);
                                                }
                                            }
                                        }

                                        $userInsert = [
                                            'group_id' => $group_id,
                                            'school_id' => $schoolId,
                                            'class_id' => $classId,
                                            'username' => $user->username,
                                            'nickname' => $user->nickname,
                                            'password' => $user->password,
                                            'salt' => $user->salt,
                                            'mobile' => $user->mobile,
                                            'avatar' => $user->avatar,
                                            'createtime' => $user->createtime,
                                            'status' => $user->status,
                                        ];
                                        if(!$row) {
                                            $insertId = $query->name('user')->insertGetId($userInsert);
                                            $formmaster_insert_num++;
                                            \think\Log::info("formmaster用户插入成功", ['username' => $user->username, 'user_id' => $insertId]);
                                        } else {
                                            \think\Log::info("formmaster用户已存在", ['username' => $user->username]);
                                            continue;
                                        }
                                    } catch (\Exception $e) {
                                        \think\Log::error("formmaster用户处理失败", ['username' => $user->username, 'error' => $e->getMessage()]);
                                        continue;
                                    }
                                }

                                $syncResults[] = [
                                    'platform' => $platform->name,
                                    'num' => $formmaster_insert_num,
                                ];
                            } catch (\Exception $e) {
                                \think\Log::error("formmaster平台同步失败", ['error' => $e->getMessage(), 'product_id' => $item->product_id]);
                                $syncResults[] = [
                                    'platform' => $platform->name,
                                    'num' => 0,
                                    'error' => $e->getMessage()
                                ];
                            }
                            break;
                        case 'examadmin':
                            try {
                                $query = Db::connect('examadmin');
                                $time = time();
                                foreach($userArr as $user) {
                                    try {
                                        $insert = [];
                                        # 7：教师 2：学生
                                        $group_id = $user->group_id == 2 ? 7 : 2;
                                        $row = $query->name('user')->where(['username' => $user->username])->find();
                                        $userInsert = [
                                            'group_id' => $group_id,
                                            'username' => $user->username,
                                            'nickname' => $user->nickname,
                                            'password' => $user->password,
                                            'salt' => $user->salt,
                                            'mobile' => $user->mobile,
                                            'avatar' => $user->avatar,
                                            'createtime' => $user->createtime,
                                            'status' => $user->status,
                                        ];

                                        $insertId = null;
                                        if(!$row) {
                                            $insertId = $query->name('user')->insertGetId($userInsert);
                                            $examadmin_insert_num++;
                                            \think\Log::info("examadmin用户插入成功", ['username' => $user->username, 'user_id' => $insertId]);
                                        } else {
                                            $insertId = $row['id'];
                                            \think\Log::info("examadmin用户已存在", ['username' => $user->username, 'user_id' => $insertId]);
                                        }

                                        $proId = $classId = $gradeId = $schoolId = null;
                                        $schoolId = $query->name('school')->where(['name' => $customer_name])->value('id');
                                        if (!$schoolId) {
                                            \think\Log::warning("examadmin未找到学校信息", ['customer_name' => $customer_name]);
                                            continue;
                                        }

                                        # 学生获得班级等信息
                                        if($group_id != 7) {
                                            # 获取用户组织信息
                                            $org = \app\admin\model\system\Org::where('org_id', $user->org_id)->find();
                                            if(!empty($org)) {
                                                $org1 = \app\admin\model\system\Org::where('org_id', $org->parent_id)->find();
                                                $org2 = \app\admin\model\system\Org::where('org_id', $org1->parent_id)->find();

                                                # 用户专业
                                                $proId = $query->name('profession')->where(['school_id'=>$schoolId,'name' => $org2['org_name']])->value('id');
                                                if(!$proId) {
                                                    $proId = $query->name('profession')->insertGetId(['school_id'=>$schoolId,'name' => $org2['org_name'],'createtime'=>$time]);
                                                }
                                                # 用户年级
                                                $gradeId = $query->name('grade')->where(['pro_id'=> $proId,'name' => $org1['org_name']])->value('id');
                                                if(!$gradeId) {
                                                    $gradeId = $query->name('grade')->insertGetId(['pro_id'=>$proId,'name' => $org1['org_name'],'createtime'=>$time]);
                                                }

                                                # 用户班级
                                                $classId = $query->name('classes')->where(['grade_id'=>$gradeId,'name' => $org['org_name']])->value('id');
                                                if(!$classId) {
                                                    $classId = $query->name('classes')->insertGetId(['grade_id'=>$gradeId,'name' => $org['org_name'],'status'=>10,'createtime'=>$time]);
                                                }
                                            }
                                        }

                                        # 用户扩展表
                                        $schoolUserRow = $query->name('school_user')->where(['user_id' => $insertId])->find();
                                        if($schoolUserRow) {
                                            $update = [
                                                'pro_id' => $proId,
                                                'class_id' => $classId,
                                                'grade_id' => $gradeId,
                                                'school_id' => $schoolId,
                                                'updatetime' => time(),
                                            ];
                                            $query->name('school_user')->where(['user_id' => $insertId])->update($update);
                                        } else {
                                            $insertData = [
                                                'user_id' => $insertId,
                                                'pro_id' => $proId,
                                                'classes_id' => $classId,
                                                'grade_id' => $gradeId,
                                                'school_id' => $schoolId,
                                            ];
                                            $query->name('school_user')->insert($insertData);
                                        }
                                    } catch (\Exception $e) {
                                        \think\Log::error("examadmin用户处理失败", ['username' => $user->username, 'error' => $e->getMessage()]);
                                        continue;
                                    }
                                }

                                $syncResults[] = [
                                    'platform' => $platform->name,
                                    'num' => $examadmin_insert_num,
                                ];
                            } catch (\Exception $e) {
                                \think\Log::error("examadmin平台同步失败", ['error' => $e->getMessage(), 'product_id' => $item->product_id]);
                                $syncResults[] = [
                                    'platform' => $platform->name,
                                    'num' => 0,
                                    'error' => $e->getMessage()
                                ];
                            }
                            break;
                        case 'case':
                            try {
                                $query = Db::connect('vtrs');
                                $time = time();
                                foreach($userArr as $user) {
                                    try {
                                        $insert = [];
                                        $group_id = $user->group_id == 2 ? 2 : 1;
                                        $row = $query->name('user')->where(['username' => $user->username])->find();
                                        $userInsert = [
                                            'group_id' => $group_id,
                                            'username' => $user->username,
                                            'nickname' => $user->nickname,
                                            'password' => $user->password,
                                            'salt' => $user->salt,
                                            'mobile' => $user->mobile,
                                            'avatar' => $user->avatar,
                                            'createtime' => $user->createtime,
                                            'status' => $user->status,
                                        ];

                                        $insertId = null;
                                        if(!$row) {
                                            $insertId = $query->name('user')->insertGetId($userInsert);
                                            $case_insert_num++;
                                            \think\Log::info("case用户插入成功", ['username' => $user->username, 'user_id' => $insertId]);
                                        } else {
                                            $insertId = $row['id'];
                                            \think\Log::info("case用户已存在", ['username' => $user->username, 'user_id' => $insertId]);
                                        }

                                        $proId = $classId = $gradeId = $schoolId = null;
                                        $schoolId = $query->name('user_school')->where(['name' => $customer_name])->value('id');
                                        if (!$schoolId) {
                                            \think\Log::warning("case未找到学校信息", ['customer_name' => $customer_name]);
                                            continue;
                                        }
                                        $productIds = $item->subsystem_id;

                                        # 学生获得班级等信息
                                        if($group_id != 2) {
                                            # 获取用户组织信息
                                            $org = \app\admin\model\system\Org::where('org_id', $user->org_id)->find();
                                            if(!empty($org)) {
                                                $org1 = \app\admin\model\system\Org::where('org_id', $org->parent_id)->find();
                                                $org2 = \app\admin\model\system\Org::where('org_id', $org1->parent_id)->find();

                                                # 用户专业
                                                $proId = $query->name('user_profession')->where(['school_id'=>$schoolId,'name' => $org2['org_name']])->value('id');
                                                if(!$proId) {
                                                    $proId = $query->name('user_profession')->insertGetId(['school_id'=>$schoolId,'name' => $org2['org_name'],'status'=>1,'createtime'=>$time]);
                                                }
                                                # 用户年级
                                                $gradeId = $query->name('user_grade')->where(['pro_id'=> $proId,'name' => $org1['org_name']])->value('id');
                                                if(!$gradeId) {
                                                    $gradeId = $query->name('user_grade')->insertGetId(['pro_id'=>$proId,'name' => $org1['org_name'],'status'=>1,'createtime'=>$time]);
                                                }

                                                # 用户班级
                                                $classId = $query->name('user_classes')->where(['grade_id'=>$gradeId,'name' => $org['org_name']])->value('id');
                                                if(!$classId) {
                                                    $classId = $query->name('user_classes')->insertGetId(['grade_id'=>$gradeId,'pro_id'=>$proId,'school_id'=>$schoolId,'name' => $org['org_name'],'status'=>1,'createtime'=>$time]);
                                                }
                                            }
                                        }

                                        # 用户扩展表
                                        $userProductRow = $query->name('user_product')->where(['user_id' => $insertId])->find();
                                        if($userProductRow) {
                                            $update = [
                                                'pro_id' => $proId,
                                                'class_id' => $classId,
                                                'grade_id' => $gradeId,
                                                'school_id' => $schoolId,
                                                'product_ids' => $productIds,
                                                'updatetime' => time(),
                                            ];
                                            $query->name('user_product')->where(['user_id' => $insertId])->update($update);
                                        } else {
                                            $insertData = [
                                                'user_id' => $insertId,
                                                'pro_id' => $proId,
                                                'class_id' => $classId,
                                                'grade_id' => $gradeId,
                                                'school_id' => $schoolId,
                                                'product_ids' => $productIds,
                                                'createtime' => time(),
                                            ];
                                            $query->name('user_product')->insert($insertData);
                                        }
                                    } catch (\Exception $e) {
                                        \think\Log::error("case用户处理失败", ['username' => $user->username, 'error' => $e->getMessage()]);
                                        continue;
                                    }
                                }

                                $syncResults[] = [
                                    'platform' => $platform->name,
                                    'num' => $case_insert_num,
                                ];
                            } catch (\Exception $e) {
                                \think\Log::error("case平台同步失败", ['error' => $e->getMessage(), 'product_id' => $item->product_id]);
                                $syncResults[] = [
                                    'platform' => $platform->name,
                                    'num' => 0,
                                    'error' => $e->getMessage()
                                ];
                            }
                            break;

                        default:
                            \think\Log::warning("未知平台类型", ['platform' => $platform->config_name, 'product_id' => $item->product_id]);
                            $syncResults[] = [
                                'platform' => $platform->name,
                                'num' => 0,
                                'error' => '未知平台类型: ' . $platform->config_name
                            ];
                            break;
                    }

                    \think\Log::info("产品同步完成", [
                        'product_id' => $item->product_id,
                        'platform' => $platform->config_name,
                        'result' => end($syncResults)
                    ]);

                } catch (\Exception $e) {
                    \think\Log::error("产品同步失败", [
                        'product_id' => $item->product_id,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                    continue;
                }
            }

            // 记录同步完成日志
            \think\Log::info("授权信息同步完成", [
                'auth_id' => $auth_id,
                'total_products' => count($product),
                'sync_results' => $syncResults
            ]);

            return $syncResults;

        } catch (\Exception $e) {
            \think\Log::error("同步授权信息失败", [
                'auth_id' => $auth_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }
}
