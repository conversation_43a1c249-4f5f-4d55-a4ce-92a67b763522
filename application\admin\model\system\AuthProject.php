<?php

namespace app\admin\model\system;

use think\Model;
use traits\model\SoftDelete;
use think\Db;

class AuthProject extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'auth_project';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'status_text'
    ];
    

    
    public function getStatusList()
    {
        return ['normal' => __('Normal'), 'closed' => __('Hidden')];
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }




    public function customer()
    {
        return $this->belongsTo('app\admin\model\Customer', 'customer_id', 'customer_id', [], 'LEFT')->setEagerlyType(0);
    }


    public function admin()
    {
        return $this->belongsTo('app\admin\model\Admin', 'admin_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
    

    /**
     * 同步授权信息到子系统数据库
     * @param $auth_id int 授权项目ID
     */
    public function syncAuthorization($auth_id){
        # 出货单产品列表
        $customer_id = \app\admin\model\system\AuthProject::where('auth_id', $auth_id)->value('customer_id');
        $outboundList = Db::name('auth_outbound')->where('auth_id', $auth_id)->select();
        $outboundPrduct = [];
        foreach($outboundList as $outbound){
            $outboundPrductRow = Db::name('outbound_product')->where('outbound_id', $outbound['outbound_id'])->column('product_id');
            $outboundPrduct = array_merge($outboundPrduct, $outboundPrductRow);
        }
        $product = \app\admin\model\system\Product::where('product_id', 'in', $outboundPrduct)->select();
        $ai_insert_num = $vtrs_insert_num = $formmaster_insert_num = $examadmin_insert_num = $case_insert_num = 0;
        foreach($product as $item){
            $auth = Db::name('auth_project_authorization')->where(['auth_id'=>$auth_id,'product_id'=>$item->product_id])->find();
            $where = [];
            if($auth && $auth['visibility'] == 2){
                $where = [
                    'id'=>['in', explode(',', $auth['user_ids'])],
                    'customer_id' => $customer_id,
                ];
            }else{
                $where = [
                    'customer_id' => $customer_id,
                ];
            }
            # 获取授权用户
            $userArr = \app\admin\model\User::where($where)->select();

            # 获取平台信息
            $platform = \app\admin\model\system\Platform::where('id', $item->platform_id)->find();
            $customer_name = \app\admin\model\Customer::where('customer_id', $customer_id)->value('customer_name');
            $return = [];
            switch ($platform->config_name) {
                case 'aimaster':
                    $query = Db::connect('aimaster');
                    $insert = [];
                    foreach($userArr as $user)
                    {
                        $group_id = $user->group_id == 2 ? 1 : 2;
                        $row = $query->name('user')->where(['project_id' => $item->subsystem_id,'username' => $user->username])->find();
                        $userInsert = [
                            'group_id' => $group_id,
                            'project_id' => $item->subsystem_id,
                            'username' => $user->username,
                            'nickname' => $user->nickname,
                            'password' => $user->password,
                            'salt' => $user->salt,
                            'mobile' => $user->mobile,
                            'avatar' => $user->avatar,
                            'createtime' => $user->createtime,
                            'status' => $user->status,
                        ];
                        if(!$row)
                        {
                            $insert[] = $userInsert;
                            $ai_insert_num++;
                        }else{
                            continue;
                            # 是否更新用户信息
                            $query->name('user')->where('id',$row['id'])->update($userInsert);
                        }
                    }
                    if($insert)
                    {
                        $query->name('user')->insertAll($insert);
                    }
                    $return[] = [
                        'platform' => $platform->name,
                        'num' => $ai_insert_num,
                    ];
                break;
                case 'vtrs':
                    $query = Db::connect('vtrs');
                    $time = time();
                    foreach($userArr as $user)
                    {
                        $insert = [];
                        $group_id = $user->group_id == 2 ? 2 : 1;
                        $row = $query->name('user')->where(['username' => $user->username])->find();
                        $userInsert = [
                            'group_id' => $group_id,
                            'username' => $user->username,
                            'nickname' => $user->nickname,
                            'password' => $user->password,
                            'salt' => $user->salt,
                            'mobile' => $user->mobile,
                            'avatar' => $user->avatar,
                            'createtime' => $user->createtime,
                            'status' => $user->status,
                        ];
                        if(!$row)
                        {
                            $insertId = $query->name('user')->insertGetId($userInsert);
                            $vtrs_insert_num++;
                        }else{
                            continue;
                            # 是否更新用户信息
                            // $query->name('user')->where('id',$row['id'])->update($userInsert);
                        }
                        $proId = $classId = $gradeId = $schoolId = null;
                        $schoolId = $query->name('user_school')->where(['name' => $customer_name])->value('id');
                        $productIds = $item->subsystem_id;

                        # 学生获得班级等信息
                        if($group_id != 2)
                        {
                            # 获取用户组织信息
                            $org = \app\admin\model\system\Org::where('org_id', $user->org_id)->find();
                            if(!empty($org))
                            {
                                $org1 = \app\admin\model\system\Org::where('org_id', $org->parent_id)->find();
                                $org2 = \app\admin\model\system\Org::where('org_id', $org1->parent_id)->find();

                                # 用户专业
                                $proId = $query->name('user_profession')->where(['school_id'=>$schoolId,'name' => $org2['org_name']])->value('id');
                                if(!$proId)
                                {
                                    $proId = $query->name('user_profession')->insertGetId(['school_id'=>$schoolId,'name' => $org2['org_name'],'status'=>1,'createtime'=>$time]);
                                }
                                # 用户年级
                                $gradeId = $query->name('user_grade')->where(['pro_id'=> $proId,'name' => $org1['org_name']])->value('id');
                                if(!$gradeId)
                                {
                                    $gradeId = $query->name('user_grade')->insertGetId(['pro_id'=>$proId,'name' => $org1['org_name'],'status'=>1,'createtime'=>$time]);
                                }

                                # 用户班级
                                $classId = $query->name('user_classes')->where(['grade_id'=>$gradeId,'name' => $org['org_name']])->value('id');
                                if(!$classId)
                                {
                                    $classId = $query->name('user_classes')->insertGetId(['grade_id'=>$gradeId,'pro_id'=>$proId,'school_id'=>$schoolId,'name' => $org['org_name'],'status'=>1,'createtime'=>$time]);
                                }
                            }
                        }

                        # 用户扩展表
                        $row = $query->name('user_product')->where(['user_id' => $insertId])->find();
                        if($row) {
                            $update = [
                                'pro_id' => $proId,
                                'class_id' => $classId,
                                'grade_id' => $gradeId,
                                'school_id' => $schoolId,
                                'product_ids' => $productIds,
                                'updatetime' => time(),
                            ];
            
                            $query->name('user_product')->where(['user_id' => $insertId])->update($update);
            
                        }else{
                            $insert = [
                                'user_id' => $insertId,
                                'pro_id' => $proId,
                                'class_id' => $classId,
                                'grade_id' => $gradeId,
                                'school_id' => $schoolId,
                                'product_ids' => $productIds,
                                'createtime' => time(),
                            ];
                            
                            $query->name('user_product')->insert($insert);
            
                        }
                    }
                    $return[] = [
                        'platform' => $platform->name,
                        'num' => $vtrs_insert_num,
                    ];
                break;
                case 'formmaster':
                    $query = Db::connect('formmaster');
                    $time = time();
                    foreach($userArr as $user)
                    {
                        $insert = [];
                        $group_id = $user->group_id == 2 ? 2 : 1;
                        $row = $query->name('user')->where(['username' => $user->username])->find();
                        
                        $proId = $classId = $gradeId = $schoolId = null;
                        $schoolId = $query->name('school')->where(['name' => $customer_name])->value('id');

                        # 学生获得班级等信息
                        if($group_id != 2)
                        {
                            # 获取用户组织信息
                            $org = \app\admin\model\system\Org::where('org_id', $user->org_id)->find();
                            if(!empty($org))
                            {
                                $org1 = \app\admin\model\system\Org::where('org_id', $org->parent_id)->find();
                                $org2 = \app\admin\model\system\Org::where('org_id', $org1->parent_id)->find();

                                # 用户专业
                                $proId = $query->name('profession')->where(['school_id'=>$schoolId,'name' => $org2['org_name']])->value('id');
                                if(!$proId)
                                {
                                    $proId = $query->name('profession')->insertGetId(['school_id'=>$schoolId,'name' => $org2['org_name'],'createtime'=>$time]);
                                }
                                # 用户年级
                                $gradeId = $query->name('grade')->where(['profession_id'=> $proId,'name' => $org1['org_name']])->value('id');
                                if(!$gradeId)
                                {
                                    $gradeId = $query->name('grade')->insertGetId(['profession_id'=>$proId,'name' => $org1['org_name'],'createtime'=>$time]);
                                }

                                # 用户班级
                                $classId = $query->name('classes')->where(['grade_id'=>$gradeId,'name' => $org['org_name']])->value('id');
                                if(!$classId)
                                {
                                    $classId = $query->name('grade')->insertGetId(['grade_id'=>$gradeId,'pro_id'=>$proId,'school_id'=>$schoolId,'name' => $org['org_name'],'createtime'=>$time]);
                                }
                            }
                        }

                        $userInsert = [
                            'group_id' => $group_id,
                            'school_id' => $schoolId,
                            'class_id' => $classId,
                            'username' => $user->username,
                            'nickname' => $user->nickname,
                            'password' => $user->password,
                            'salt' => $user->salt,
                            'mobile' => $user->mobile,
                            'avatar' => $user->avatar,
                            'createtime' => $user->createtime,
                            'status' => $user->status,
                        ];
                        if(!$row)
                        {
                            $insertId = $query->name('user')->insertGetId($userInsert);
                            $formmaster_insert_num++;
                        }else{
                            continue;
                            # 是否更新用户信息
                            // $query->name('user')->where('id',$row['id'])->update($userInsert);
                        }
                    }
                    $return[] = [
                        'platform' => $platform->name,
                        'num' => $formmaster_insert_num,
                    ];
                break;
                case 'examadmin':
                    $query = Db::connect('examadmin');
                    $time = time();
                    foreach($userArr as $user)
                    {
                        $insert = [];
                        # 7：教师 2：学生
                        $group_id = $user->group_id == 2 ? 7 : 2;
                        $row = $query->name('user')->where(['username' => $user->username])->find();
                        $userInsert = [
                            'group_id' => $group_id,
                            'username' => $user->username,
                            'nickname' => $user->nickname,
                            'password' => $user->password,
                            'salt' => $user->salt,
                            'mobile' => $user->mobile,
                            'avatar' => $user->avatar,
                            'createtime' => $user->createtime,
                            'status' => $user->status,
                        ];
                        if(!$row)
                        {
                            $insertId = $query->name('user')->insertGetId($userInsert);
                            $examadmin_insert_num++;
                        }else{
                            continue;
                            # 是否更新用户信息
                            // $query->name('user')->where('id',$row['id'])->update($userInsert);
                        }
                        $proId = $classId = $gradeId = $schoolId = null;
                        $schoolId = $query->name('school')->where(['name' => $customer_name])->value('id');

                        # 学生获得班级等信息
                        if($group_id != 7)
                        {
                            # 获取用户组织信息
                            $org = \app\admin\model\system\Org::where('org_id', $user->org_id)->find();
                            if(!empty($org))
                            {
                                $org1 = \app\admin\model\system\Org::where('org_id', $org->parent_id)->find();
                                $org2 = \app\admin\model\system\Org::where('org_id', $org1->parent_id)->find();

                                # 用户专业
                                $proId = $query->name('profession')->where(['school_id'=>$schoolId,'name' => $org2['org_name']])->value('id');
                                if(!$proId)
                                {
                                    $proId = $query->name('profession')->insertGetId(['school_id'=>$schoolId,'name' => $org2['org_name'],'createtime'=>$time]);
                                }
                                # 用户年级
                                $gradeId = $query->name('grade')->where(['pro_id'=> $proId,'name' => $org1['org_name']])->value('id');
                                if(!$gradeId)
                                {
                                    $gradeId = $query->name('grade')->insertGetId(['pro_id'=>$proId,'name' => $org1['org_name'],'createtime'=>$time]);
                                }

                                # 用户班级
                                $classId = $query->name('classes')->where(['grade_id'=>$gradeId,'name' => $org['org_name']])->value('id');
                                if(!$classId)
                                {
                                    $classId = $query->name('classes')->insertGetId(['grade_id'=>$gradeId,'name' => $org['org_name'],'status'=>10,'createtime'=>$time]);
                                }
                            }
                        }

                        # 用户扩展表
                        $row = $query->name('school_user')->where(['user_id' => $insertId])->find();
                        if($row) {
                            $update = [
                                'pro_id' => $proId,
                                'class_id' => $classId,
                                'grade_id' => $gradeId,
                                'school_id' => $schoolId,
                                'updatetime' => time(),
                            ];
            
                            $query->name('school_user')->where(['user_id' => $insertId])->update($update);
            
                        }else{
                            $insert = [
                                'user_id' => $insertId,
                                'pro_id' => $proId,
                                'classes_id' => $classId,
                                'grade_id' => $gradeId,
                                'school_id' => $schoolId,
                            ];
                            
                            $query->name('school_user')->insert($insert);
            
                        }
                    }
                    $return[] = [
                        'platform' => $platform->name,
                        'num' => $examadmin_insert_num,
                    ];
                break;
                case 'case':
                    $query = Db::connect('vtrs');
                    $time = time();
                    foreach($userArr as $user)
                    {
                        $insert = [];
                        $group_id = $user->group_id == 2 ? 2 : 1;
                        $row = $query->name('user')->where(['username' => $user->username])->find();
                        $userInsert = [
                            'group_id' => $group_id,
                            'username' => $user->username,
                            'nickname' => $user->nickname,
                            'password' => $user->password,
                            'salt' => $user->salt,
                            'mobile' => $user->mobile,
                            'avatar' => $user->avatar,
                            'createtime' => $user->createtime,
                            'status' => $user->status,
                        ];
                        if(!$row)
                        {
                            $insertId = $query->name('user')->insertGetId($userInsert);
                            $case_insert_num++;
                        }else{
                            continue;
                            # 是否更新用户信息
                            // $query->name('user')->where('id',$row['id'])->update($userInsert);
                        }
                        $proId = $classId = $gradeId = $schoolId = null;
                        $schoolId = $query->name('user_school')->where(['name' => $customer_name])->value('id');
                        $productIds = $item->subsystem_id;

                        # 学生获得班级等信息
                        if($group_id != 2)
                        {
                            # 获取用户组织信息
                            $org = \app\admin\model\system\Org::where('org_id', $user->org_id)->find();
                            if(!empty($org))
                            {
                                $org1 = \app\admin\model\system\Org::where('org_id', $org->parent_id)->find();
                                $org2 = \app\admin\model\system\Org::where('org_id', $org1->parent_id)->find();

                                # 用户专业
                                $proId = $query->name('user_profession')->where(['school_id'=>$schoolId,'name' => $org2['org_name']])->value('id');
                                if(!$proId)
                                {
                                    $proId = $query->name('user_profession')->insertGetId(['school_id'=>$schoolId,'name' => $org2['org_name'],'status'=>1,'createtime'=>$time]);
                                }
                                # 用户年级
                                $gradeId = $query->name('user_grade')->where(['pro_id'=> $proId,'name' => $org1['org_name']])->value('id');
                                if(!$gradeId)
                                {
                                    $gradeId = $query->name('user_grade')->insert(['pro_id'=>$proId,'name' => $org1['org_name'],'status'=>1,'createtime'=>$time]);
                                }

                                # 用户班级
                                $classId = $query->name('user_classes')->where(['grade_id'=>$gradeId,'name' => $org['org_name']])->value('id');
                                if(!$classId)
                                {
                                    $classId = $query->name('user_classes')->insert(['grade_id'=>$gradeId,'pro_id'=>$proId,'school_id'=>$schoolId,'name' => $org['org_name'],'status'=>1,'createtime'=>$time]);
                                }
                            }
                        }

                        # 用户扩展表
                        $row = $query->name('user_product')->where(['user_id' => $insertId])->find();
                        if($row) {
                            $update = [
                                'pro_id' => $proId,
                                'class_id' => $classId,
                                'grade_id' => $gradeId,
                                'school_id' => $schoolId,
                                'product_ids' => $productIds,
                                'updatetime' => time(),
                            ];
            
                            $query->name('user_product')->where(['user_id' => $insertId])->update($update);
            
                        }else{
                            $insert = [
                                'user_id' => $insertId,
                                'pro_id' => $proId,
                                'class_id' => $classId,
                                'grade_id' => $gradeId,
                                'school_id' => $schoolId,
                                'product_ids' => $productIds,
                                'createtime' => time(),
                            ];
                            
                            $query->name('user_product')->insert($insert);
            
                        }
                    }
                    $return[] = [
                        'platform' => $platform->name,
                        'num' => $case_insert_num,
                    ];
                break;
            }
            return $return;
        }
    }
}
