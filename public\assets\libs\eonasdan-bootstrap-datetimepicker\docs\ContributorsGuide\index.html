﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    
    
    <link rel="apple-touch-icon" sizes="57x57" href="/apple-touch-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="114x114" href="/apple-touch-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="72x72" href="/apple-touch-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="144x144" href="/apple-touch-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="60x60" href="/apple-touch-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="120x120" href="/apple-touch-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="76x76" href="/apple-touch-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="152x152" href="/apple-touch-icon-152x152.png">
    <link rel="icon" type="image/png" href="/favicon-196x196.png" sizes="196x196">
    <link rel="icon" type="image/png" href="/favicon-160x160.png" sizes="160x160">
    <link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96">
    <link rel="icon" type="image/png" href="/favicon-16x16.png" sizes="16x16">
    <link rel="icon" type="image/png" href="/favicon-32x32.png" sizes="32x32">
    <meta name="msapplication-TileColor" content="#2b5797">
    <meta name="msapplication-TileImage" content="/mstile-144x144.png">

    <title></title>

    <link rel="stylesheet" type="text/css" media="screen"
          href="//maxcdn.bootstrapcdn.com/bootstrap/3.3.1/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="//maxcdn.bootstrapcdn.com/font-awesome/4.3.0/css/font-awesome.min.css">
    <link href="../css/prettify-1.0.css" rel="stylesheet">
    <link href="../css/base.css" rel="stylesheet">
    <link href="//cdn.rawgit.com/Eonasdan/bootstrap-datetimepicker/e8bddc60e73c1ec2475f827be36e1957af72e2ea/build/css/bootstrap-datetimepicker.css" rel="stylesheet">

    <script type="text/javascript" src="//code.jquery.com/jquery-2.1.1.min.js"></script>
    <script type="text/javascript" src="//maxcdn.bootstrapcdn.com/bootstrap/3.3.1/js/bootstrap.min.js"></script>
    
    <script src="//cdnjs.cloudflare.com/ajax/libs/moment.js/2.9.0/moment-with-locales.js"></script>
    
    
    <script src="//cdn.rawgit.com/Eonasdan/bootstrap-datetimepicker/e8bddc60e73c1ec2475f827be36e1957af72e2ea/src/js/bootstrap-datetimepicker.js"></script>
    
    
    
</head>

<body>

<div class="navbar navbar-default navbar-fixed-top" role="navigation">
    <div class="container">

        <!-- Collapsed navigation -->
        <div class="navbar-header">
            <!-- Expander button -->
            <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>

            <!-- Main title -->
            <a class="navbar-brand" href=""></a>
        </div>

        <!-- Expanded navigation -->
        <div class="navbar-collapse collapse">
            <!-- Main navigation -->
            <ul class="nav navbar-nav">
            
            
                <li >
                    <a href="..">Usage</a>
                </li>
            
            
            
                <li >
                    <a href="../Installing/">Installing</a>
                </li>
            
            
            
                <li >
                    <a href="../Functions/">Functions</a>
                </li>
            
            
            
                <li >
                    <a href="../Options/">Options</a>
                </li>
            
            
            
                <li >
                    <a href="../Events/">Events</a>
                </li>
            
            
            
                <li >
                    <a href="../Changelog.md">Change Log</a>
                </li>
            
            
            
                <li class="active">
                    <a href="./">Dev Guide</a>
                </li>
            
            
            
                <li >
                    <a href="../Extras/">Extras</a>
                </li>
            
            
            
                <li >
                    <a href="../FAQ/">FAQs</a>
                </li>
            
            
            </ul>

            <!-- Search, Navigation and Repo links -->
            <ul class="nav navbar-nav navbar-right">
                
				
                
            </ul>
        </div>
    </div>
</div>

<div class="container">
    <div class="row">
        <div class="col-md-3"><script async type="text/javascript" src="//cdn.carbonads.com/carbon.js?serve=CK7DC5QN&placement=eonasdangithubio" id="_carbonads_js"></script>
<div class="bs-sidebar hidden-print affix well" role="complementary">    <ul class="nav bs-sidenav">
    
        <li class="main active"><a href="#introduction">Introduction</a></li>
        
    
        <li class="main "><a href="#code">Code</a></li>
        
            <li><a href="#private-variables">Private variables</a></li>
        
            <li><a href="#private-functions">Private functions</a></li>
        
    
    </ul>
</div></div>
        <div class="col-md-8" role="main">
            <div class="alert alert-danger" style="font-size:1.5em;">
                <strong>Important!</strong>
                Please read this <a href="https://eonasdan.com/posts/state-of-my-picker" target="_blank">blog post</a>
            </div>
            

<p>This guide is aimed to contributors wishing to understand the internals of the code in order to change/evolve the component. </p>
<p><strong>Note:</strong> this guide refers to <strong>version 4</strong> which is currently in beta and will be updated as we progress</p>
<h2 id="introduction">Introduction</h2>
<p>This component consists actually of 2 subcomponent UI widgets one for the date and one for the time selection process. The developers can configure which of those are needed and also the granularity that the component will allow the users to select a date/time. Developers also choose the format that the selected datetime will be displayed in the input field.
The component uses on <code>jQuery</code>, <code>moment.js</code> and <code>bootstrap</code> libraries.</p>
<h2 id="code">Code</h2>
<h3 id="private-variables">Private variables</h3>
<ul>
<li>
<p><code>element</code> - Holds the DOM element this instance is attached to</p>
</li>
<li>
<p><code>options</code> - Holds an object with the curently set options for the specific instance of the component. Don't directly change the properties of that object use the public API methods instead. DO NOT expose this object or its properties outside of the component.</p>
</li>
<li>
<p><code>picker</code> - Reference variable to the created instance <code>(this)</code></p>
</li>
<li>
<p><code>date</code> - Holds the moment object for the model value of the component. <strong>DON'T</strong> directly change this variable unless you <strong>REALLY</strong> know what you are doing. Use <code>setValue()</code> function to set it. It handles all component logic for updating the model value and emitting all the appropriate events</p>
</li>
<li>
<p><code>viewDate</code> - Holds the currently selected value that the user has selected through the widget. This is not the model value this is the view value. Changing this usually requires a subsequent call to <code>update()</code> function</p>
</li>
<li>
<p><code>unset</code> - A <code>boolean</code> variable that holds wheather the components model value is set or not. Model's value starts as <code>unset = true</code> and if is either set by the user or programmatically through the api to a valid value then it is set to <code>false</code>. If subsequent events lead to an invalid value then this variable is set to <code>true</code> again. Setting this variable usually takes place in the <code>setValue()</code> function.</p>
</li>
<li>
<p><code>input</code> - Hold the DOM input element this instance is attached to</p>
</li>
<li>
<p><code>component</code> - Holds a reference to the .input-group DOM element that the widget is attached or false if it is attached directly on an input field</p>
</li>
<li>
<p><code>widget</code> - Holds a reference to the DOM element containing the widget or <code>false</code> if the widget is hidden</p>
</li>
<li>
<p><code>use24hours</code> - Holds whether the component uses 24 hours format or not. This is initialized on the <code>format()</code> function</p>
</li>
<li>
<p><code>minViewModeNumber</code> - Holds the Numeric equivelant of the options.minViewMode parameter</p>
</li>
<li>
<p><code>format</code> - Holds the current format string that is used for formating the date model value. Note this is not the same thing as the <code>options.format</code> as the second could be set to <code>false</code> in which case the first takes the locale's <code>L</code> or <code>LT</code> value</p>
</li>
<li>
<p><code>currentViewMode</code> - Hold the state of the current viewMode for the DatePicker subcomponent</p>
</li>
<li>
<p><code>actions</code> - An object containing all the functions that can be called when the users clicks on the widget</p>
</li>
<li>
<p><code>datePickerModes</code> - An array of objects with configuration parameters for the different views of the DatePicker subcomponent</p>
</li>
<li>
<p><code>viewModes</code> - An array of strings containing all the possible strings that <code>options.viewMode</code> can take through <code>viewMode()</code> public api function</p>
</li>
<li>
<p><code>directionModes</code> - An array of strings containing all the possible strings that <code>options.direction</code> can take through <code>direction()</code> public api function</p>
</li>
<li>
<p><code>orientationModes</code> - An array of strings containing all the possible strings that <code>options.orientation</code> can take through <code>orientation()</code> public api function</p>
</li>
</ul>
<h3 id="private-functions">Private functions</h3>
<h4 id="widget-related">Widget related</h4>
<ul>
<li>
<p><code>getDatePickerTemplate()</code> - returns a string containing the html code for the date picker subcomponent</p>
</li>
<li>
<p><code>getTimePickerTemplate()</code> - returns a string containing the html code for the time picker subcomponent</p>
</li>
<li>
<p><code>getTemplate()</code> - returns a string with containing the html code for all the DateTimePicker component</p>
</li>
<li>
<p><code>place()</code> - handles the placement of the widget's dropdown</p>
</li>
<li>
<p><code>updateMonths()</code> - updates the html subpage related to the months for Date picker view</p>
</li>
<li>
<p><code>updateYears()</code> - updates the html subpage related to the years for Date picker view</p>
</li>
<li>
<p><code>fillDate()</code> - updates the html subpage related to the days for Date picker view</p>
</li>
<li>
<p><code>fillHours()</code> - Creates the hours spans for the hours subview of the Time subcomponent</p>
</li>
<li>
<p><code>fillMinutes()</code> - Creates the minutes spans for the hours subview of the Time subcomponent</p>
</li>
<li>
<p><code>fillSeconds()</code> - Creates the seconds spans for the hours subview of the Time subcomponent</p>
</li>
<li>
<p><code>fillTime()</code> - Creates the main subview of the Time subcomponent</p>
</li>
<li>
<p><code>update()</code> - updates the UI of part of the widget</p>
</li>
<li>
<p><code>fillDow()</code> - Creates the day names in the days subview on the Date subcomponent</p>
</li>
<li>
<p><code>fillMonths()</code> - Creates the month spans for the months subview of the Date subcomponent</p>
</li>
<li>
<p><code>createWidget()</code> - creates the UI widget end attaches widget event listeners</p>
</li>
<li>
<p><code>destroyWidget()</code> - destroys the UI widget DOM element and detaches widget event listeners</p>
</li>
<li>
<p><code>showMode(dir)</code> - toggles between the various subpage related views of the DateTimePicker</p>
</li>
</ul>
<h4 id="events-related">Events related</h4>
<ul>
<li>
<p><code>notifyEvent(e)</code> - Use this function when you want to send en event to listener this could be used as a filter later</p>
</li>
<li>
<p><code>stopEvent(e)</code> - Shortcut for stopping propagation of events</p>
</li>
<li>
<p><code>doAction(e)</code> - Proxy function to call all the UI related click events</p>
</li>
<li>
<p><code>keydown(e)</code> - Function to trap </p>
</li>
<li>
<p><code>change(e)</code> - Listener function to track change events occuring on the <code>input</code> dom element the component is attached to</p>
</li>
<li>
<p><code>attachDatePickerElementEvents()</code> - Attaches listeners to the existing DOM elements the component is attached to. Called upon construction of each datetimepicker instance</p>
</li>
<li>
<p><code>detachDatePickerElementEvents()</code> - Detaches listeners from the DOM element the component is attached to. Called on <code>destroy()</code></p>
</li>
<li>
<p><code>attachDatePickerWidgetEvents()</code> - Attaches listeners on the components widget. Called on <code>show()</code></p>
</li>
<li>
<p><code>detachDatePickerWidgetEvents()</code> - Detaches listeners on the components widget. Called on <code>hide()</code></p>
</li>
</ul>
<h4 id="model-related">Model related</h4>
<ul>
<li>
<p><code>setValue(targetMoment)</code> - Sets the model value of the component takes a moment object. An <code>error</code> event will be emmited if the <code>targetMoment</code> does not pass the configured validations. Otherwise the <code>date</code> variable will be set and the relevant events will be fired.</p>
</li>
<li>
<p><code>isValid(targetMoment, granularity)</code> - returns <code>true</code> if the <code>targetMoment</code> moment object is valid according to the components set validation rules (<code>min/maxDates</code>, <code>disabled/enabledDates</code> and <code>daysOfWeekDisabled</code>). You may pass a second variable to check only up the the specific granularity <code>year, month, day, hour, minute, second</code></p>
</li>
</ul>
<h4 id="utilities">Utilities</h4>
<ul>
<li>
<p><code>indexGivenDates (givenDatesArray)</code> - Function that takes the array from <code>enabledDates()</code> and <code>disabledDates()</code> public functions and stores them as object keys to enable quick lookup</p>
</li>
<li>
<p><code>isInEnableDates(date)</code> - Checks whether if the given moment object exists in the <code>options.enabledDates</code> object</p>
</li>
<li>
<p><code>isInDisableDates(date)</code> - Checks whether if the given moment object exists in the <code>options.disabledDates</code> array</p>
</li>
<li>
<p><code>dataToOptions()</code> - Parses <code>data-date-*</code> options set on the input dom element the component is attached to and returns an object with them</p>
</li>
<li>
<p><code>isInFixed()</code> - Checks if the dom element or its parents has a fixed position css rule.</p>
</li>
<li>
<p><code>parseInputDate(date)</code> - Parses a date parameter with moment using the component's <code>options.format</code> and <code>options.useStrict</code>. It returns a <code>moment</code> object or false if <code>parsedMoment#isValid()</code> returns <code>false</code>. Use this to parse date inputs from outside the component (public API calls).</p>
</li>
<li>
<p><code>init()</code> - Initializes the component. Called when the component instance is created</p>
</li>
</ul>
        </div>
    </div>
</div>



<script src="../js/prettify-1.0.min.js"></script>
<script src="../js/base.js"></script>
<script>
    if (top != self) {
        top.location.replace(self.location.href);
    }
    (function (i, s, o, g, r, a, m) {
        i['GoogleAnalyticsObject'] = r;
        i[r] = i[r] || function () {
            (i[r].q = i[r].q || []).push(arguments)
        }, i[r].l = 1 * new Date();
        a = s.createElement(o),
            m = s.getElementsByTagName(o)[0];
        a.async = 1;
        a.src = g;
        m.parentNode.insertBefore(a, m)
    })(window, document, 'script', '//www.google-analytics.com/analytics.js', 'ga');

    ga('create', 'UA-47462200-1', 'eonasdan.github.io');
    ga('send', 'pageview');
</script>
</body>
</html>