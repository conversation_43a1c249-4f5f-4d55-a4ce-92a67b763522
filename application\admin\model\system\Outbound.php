<?php

namespace app\admin\model\system;

use think\Model;
use traits\model\SoftDelete;

class Outbound extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'outbound';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'delivery_time_text',
        'status_text'
    ];
    

    
    public function getStatusList()
    {
        return ['1' => __('Status 1'), '2' => __('Status 2')];
    }


    public function getDeliveryTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['delivery_time']) ? $data['delivery_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    protected function setDeliveryTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


    public function category()
    {
        return $this->belongsTo('app\admin\model\project\Category', 'category_id', 'category_id', [], 'LEFT')->setEagerlyType(0);
    }


    public function customer()
    {
        return $this->belongsTo('app\admin\model\Customer', 'customer_id', 'customer_id', [], 'LEFT')->setEagerlyType(0);
    }


    public function method()
    {
        return $this->belongsTo('app\admin\model\deploy\Method', 'deploy_ids', 'deploy_id', [], 'LEFT')->setEagerlyType(0);
    }
}
