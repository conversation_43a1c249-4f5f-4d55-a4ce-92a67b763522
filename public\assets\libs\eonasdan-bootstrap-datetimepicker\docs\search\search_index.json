{"config": {"lang": ["en"], "min_search_length": 3, "prebuild_index": false, "separator": "[\\s\\-]+"}, "docs": [{"location": "", "text": "Bootstrap 3 Datepicker v4 Docs Get awesome Dashboard Templates Looking for a template with the datepicker ready to go? Then get check out these templates from our partners. Creative Tim Material Dashboard Pro Gaia Bootstrap Template Pro Light Bootstrap Dashboard PRO React Flatlogic Light Blue Html5 Sing App Html5 Flatlogic One Bootstrap Template Note All functions are accessed via the data attribute e.g. $('#datetimepicker').data(\"DateTimePicker\").FUNCTION() Minimum Setup $(function () { $('#datetimepicker1').datetimepicker(); }); Code <div class=\"container\"> <div class=\"row\"> <div class='col-sm-6'> <div class=\"form-group\"> <div class='input-group date' id='datetimepicker1'> <input type='text' class=\"form-control\" /> <span class=\"input-group-addon\"> <span class=\"glyphicon glyphicon-calendar\"></span> </span> </div> </div> </div> <script type=\"text/javascript\"> $(function () { $('#datetimepicker1').datetimepicker(); }); </script> </div> </div> Using Locales $(function () { $('#datetimepicker2').datetimepicker({ locale: 'ru' }); }); Code <div class=\"container\"> <div class=\"row\"> <div class='col-sm-6'> <div class=\"form-group\"> <div class='input-group date' id='datetimepicker2'> <input type='text' class=\"form-control\" /> <span class=\"input-group-addon\"> <span class=\"glyphicon glyphicon-calendar\"></span> </span> </div> </div> </div> <script type=\"text/javascript\"> $(function () { $('#datetimepicker2').datetimepicker({ locale: 'ru' }); }); </script> </div> </div> Time Only $(function () { $('#datetimepicker3').datetimepicker({ format: 'LT' }); }); Code <div class=\"container\"> <div class=\"row\"> <div class='col-sm-6'> <div class=\"form-group\"> <div class='input-group date' id='datetimepicker3'> <input type='text' class=\"form-control\" /> <span class=\"input-group-addon\"> <span class=\"glyphicon glyphicon-time\"></span> </span> </div> </div> </div> <script type=\"text/javascript\"> $(function () { $('#datetimepicker3').datetimepicker({ format: 'LT' }); }); </script> </div> </div> Date Only $(function () { $('#datetimepicker3').datetimepicker({ format: 'L' }); }); Code <div class=\"container\"> <div class=\"row\"> <div class='col-sm-6'> <div class=\"form-group\"> <div class='input-group date' id='datetimepicker3'> <input type='text' class=\"form-control\" /> <span class=\"input-group-addon\"> <span class=\"glyphicon glyphicon-time\"></span> </span> </div> </div> </div> <script type=\"text/javascript\"> $(function () { $('#datetimepicker3').datetimepicker({ format: 'LT' }); }); </script> </div> </div> No Icon (input field only): $(function () { $('#datetimepicker4').datetimepicker(); }); Code <div class=\"container\"> <div class=\"row\"> <div class='col-sm-6'> <input type='text' class=\"form-control\" id='datetimepicker4' /> </div> <script type=\"text/javascript\"> $(function () { $('#datetimepicker4').datetimepicker(); }); </script> </div> </div> Enabled/Disabled Dates $(function () { $('#datetimepicker5').datetimepicker({ defaultDate: \"11/1/2013\", disabledDates: [ moment(\"12/25/2013\"), new Date(2013, 11 - 1, 21), \"11/22/2013 00:53\" ] }); }); Code <div class=\"container\"> <div class=\"row\"> <div class='col-sm-6'> <div class=\"form-group\"> <div class='input-group date' id='datetimepicker5'> <input type='text' class=\"form-control\" /> <span class=\"input-group-addon\"> <span class=\"glyphicon glyphicon-calendar\"></span> </span> </div> </div> </div> <script type=\"text/javascript\"> $(function () { $('#datetimepicker5').datetimepicker({ defaultDate: \"11/1/2013\", disabledDates: [ moment(\"12/25/2013\"), new Date(2013, 11 - 1, 21), \"11/22/2013 00:53\" ] }); }); </script> </div> </div> Linked Pickers $(function () { $('#datetimepicker6').datetimepicker(); $('#datetimepicker7').datetimepicker({ useCurrent: false }); $(\"#datetimepicker6\").on(\"dp.change\", function (e) { $('#datetimepicker7').data(\"DateTimePicker\").minDate(e.date); }); $(\"#datetimepicker7\").on(\"dp.change\", function (e) { $('#datetimepicker6').data(\"DateTimePicker\").maxDate(e.date); }); }); Code <div class=\"container\"> <div class='col-md-5'> <div class=\"form-group\"> <div class='input-group date' id='datetimepicker6'> <input type='text' class=\"form-control\" /> <span class=\"input-group-addon\"> <span class=\"glyphicon glyphicon-calendar\"></span> </span> </div> </div> </div> <div class='col-md-5'> <div class=\"form-group\"> <div class='input-group date' id='datetimepicker7'> <input type='text' class=\"form-control\" /> <span class=\"input-group-addon\"> <span class=\"glyphicon glyphicon-calendar\"></span> </span> </div> </div> </div> </div> <script type=\"text/javascript\"> $(function () { $('#datetimepicker6').datetimepicker(); $('#datetimepicker7').datetimepicker({ useCurrent: false //Important! See issue #1075 }); $(\"#datetimepicker6\").on(\"dp.change\", function (e) { $('#datetimepicker7').data(\"DateTimePicker\").minDate(e.date); }); $(\"#datetimepicker7\").on(\"dp.change\", function (e) { $('#datetimepicker6').data(\"DateTimePicker\").maxDate(e.date); }); }); </script> Custom Icons $(function () { $('#datetimepicker8').datetimepicker({ icons: { time: \"fa fa-clock-o\", date: \"fa fa-calendar\", up: \"fa fa-arrow-up\", down: \"fa fa-arrow-down\" } }); }); Code <div class=\"container\"> <div class=\"col-sm-6\" style=\"height:130px;\"> <div class=\"form-group\"> <div class='input-group date' id='datetimepicker8'> <input type='text' class=\"form-control\" /> <span class=\"input-group-addon\"> <span class=\"fa fa-calendar\"> </span> </span> </div> </div> </div> <script type=\"text/javascript\"> $(function () { $('#datetimepicker8').datetimepicker({ icons: { time: \"fa fa-clock-o\", date: \"fa fa-calendar\", up: \"fa fa-arrow-up\", down: \"fa fa-arrow-down\" } }); }); </script> </div> View Mode $(function () { $('#datetimepicker9').datetimepicker({ viewMode: 'years' }); }); Code <div class=\"container\"> <div class=\"col-sm-6\" style=\"height:130px;\"> <div class=\"form-group\"> <div class='input-group date' id='datetimepicker9'> <input type='text' class=\"form-control\" /> <span class=\"input-group-addon\"> <span class=\"glyphicon glyphicon-calendar\"> </span> </span> </div> </div> </div> <script type=\"text/javascript\"> $(function () { $('#datetimepicker9').datetimepicker({ viewMode: 'years' }); }); </script> </div> Min View Mode $(function () { $('#datetimepicker10').datetimepicker({ viewMode: 'years', format: 'MM/YYYY' }); }); Code <div class=\"container\"> <div class=\"col-sm-6\" style=\"height:130px;\"> <div class=\"form-group\"> <div class='input-group date' id='datetimepicker10'> <input type='text' class=\"form-control\" /> <span class=\"input-group-addon\"> <span class=\"glyphicon glyphicon-calendar\"> </span> </span> </div> </div> </div> <script type=\"text/javascript\"> $(function () { $('#datetimepicker10').datetimepicker({ viewMode: 'years', format: 'MM/YYYY' }); }); </script> </div> Disabled Days of the Week $(function () { $('#datetimepicker11').datetimepicker({ daysOfWeekDisabled: [0, 6] }); }); Code <div class=\"container\"> <div class=\"col-sm-6\" style=\"height:130px;\"> <div class=\"form-group\"> <div class='input-group date' id='datetimepicker11'> <input type='text' class=\"form-control\" /> <span class=\"input-group-addon\"> <span class=\"glyphicon glyphicon-calendar\"> </span> </span> </div> </div> </div> <script type=\"text/javascript\"> $(function () { $('#datetimepicker11').datetimepicker({ daysOfWeekDisabled: [0, 6] }); }); </script> </div> Inline $(function () { $('#datetimepicker12').datetimepicker({ inline: true, sideBySide: true }); }); Code <div style=\"overflow:hidden;\"> <div class=\"form-group\"> <div class=\"row\"> <div class=\"col-md-8\"> <div id=\"datetimepicker12\"></div> </div> </div> </div> <script type=\"text/javascript\"> $(function () { $('#datetimepicker12').datetimepicker({ inline: true, sideBySide: true }); }); </script> </div>", "title": "Usage"}, {"location": "#bootstrap-3-datepicker-v4-docs", "text": "", "title": "Bootstrap 3 Datepicker v4 Docs"}, {"location": "#minimum-setup", "text": "$(function () { $('#datetimepicker1').datetimepicker(); });", "title": "Minimum Setup"}, {"location": "#code", "text": "<div class=\"container\"> <div class=\"row\"> <div class='col-sm-6'> <div class=\"form-group\"> <div class='input-group date' id='datetimepicker1'> <input type='text' class=\"form-control\" /> <span class=\"input-group-addon\"> <span class=\"glyphicon glyphicon-calendar\"></span> </span> </div> </div> </div> <script type=\"text/javascript\"> $(function () { $('#datetimepicker1').datetimepicker(); }); </script> </div> </div>", "title": "Code"}, {"location": "#using-locales", "text": "$(function () { $('#datetimepicker2').datetimepicker({ locale: 'ru' }); });", "title": "Using Locales"}, {"location": "#code_1", "text": "<div class=\"container\"> <div class=\"row\"> <div class='col-sm-6'> <div class=\"form-group\"> <div class='input-group date' id='datetimepicker2'> <input type='text' class=\"form-control\" /> <span class=\"input-group-addon\"> <span class=\"glyphicon glyphicon-calendar\"></span> </span> </div> </div> </div> <script type=\"text/javascript\"> $(function () { $('#datetimepicker2').datetimepicker({ locale: 'ru' }); }); </script> </div> </div>", "title": "Code"}, {"location": "#time-only", "text": "$(function () { $('#datetimepicker3').datetimepicker({ format: 'LT' }); });", "title": "Time Only"}, {"location": "#code_2", "text": "<div class=\"container\"> <div class=\"row\"> <div class='col-sm-6'> <div class=\"form-group\"> <div class='input-group date' id='datetimepicker3'> <input type='text' class=\"form-control\" /> <span class=\"input-group-addon\"> <span class=\"glyphicon glyphicon-time\"></span> </span> </div> </div> </div> <script type=\"text/javascript\"> $(function () { $('#datetimepicker3').datetimepicker({ format: 'LT' }); }); </script> </div> </div>", "title": "Code"}, {"location": "#date-only", "text": "$(function () { $('#datetimepicker3').datetimepicker({ format: 'L' }); });", "title": "Date Only"}, {"location": "#code_3", "text": "<div class=\"container\"> <div class=\"row\"> <div class='col-sm-6'> <div class=\"form-group\"> <div class='input-group date' id='datetimepicker3'> <input type='text' class=\"form-control\" /> <span class=\"input-group-addon\"> <span class=\"glyphicon glyphicon-time\"></span> </span> </div> </div> </div> <script type=\"text/javascript\"> $(function () { $('#datetimepicker3').datetimepicker({ format: 'LT' }); }); </script> </div> </div>", "title": "Code"}, {"location": "#no-icon-input-field-only", "text": "$(function () { $('#datetimepicker4').datetimepicker(); });", "title": "No Icon (input field only):"}, {"location": "#code_4", "text": "<div class=\"container\"> <div class=\"row\"> <div class='col-sm-6'> <input type='text' class=\"form-control\" id='datetimepicker4' /> </div> <script type=\"text/javascript\"> $(function () { $('#datetimepicker4').datetimepicker(); }); </script> </div> </div>", "title": "Code"}, {"location": "#enableddisabled-dates", "text": "$(function () { $('#datetimepicker5').datetimepicker({ defaultDate: \"11/1/2013\", disabledDates: [ moment(\"12/25/2013\"), new Date(2013, 11 - 1, 21), \"11/22/2013 00:53\" ] }); });", "title": "Enabled/Disabled Dates"}, {"location": "#code_5", "text": "<div class=\"container\"> <div class=\"row\"> <div class='col-sm-6'> <div class=\"form-group\"> <div class='input-group date' id='datetimepicker5'> <input type='text' class=\"form-control\" /> <span class=\"input-group-addon\"> <span class=\"glyphicon glyphicon-calendar\"></span> </span> </div> </div> </div> <script type=\"text/javascript\"> $(function () { $('#datetimepicker5').datetimepicker({ defaultDate: \"11/1/2013\", disabledDates: [ moment(\"12/25/2013\"), new Date(2013, 11 - 1, 21), \"11/22/2013 00:53\" ] }); }); </script> </div> </div>", "title": "Code"}, {"location": "#linked-pickers", "text": "$(function () { $('#datetimepicker6').datetimepicker(); $('#datetimepicker7').datetimepicker({ useCurrent: false }); $(\"#datetimepicker6\").on(\"dp.change\", function (e) { $('#datetimepicker7').data(\"DateTimePicker\").minDate(e.date); }); $(\"#datetimepicker7\").on(\"dp.change\", function (e) { $('#datetimepicker6').data(\"DateTimePicker\").maxDate(e.date); }); });", "title": "Linked Pickers"}, {"location": "#code_6", "text": "<div class=\"container\"> <div class='col-md-5'> <div class=\"form-group\"> <div class='input-group date' id='datetimepicker6'> <input type='text' class=\"form-control\" /> <span class=\"input-group-addon\"> <span class=\"glyphicon glyphicon-calendar\"></span> </span> </div> </div> </div> <div class='col-md-5'> <div class=\"form-group\"> <div class='input-group date' id='datetimepicker7'> <input type='text' class=\"form-control\" /> <span class=\"input-group-addon\"> <span class=\"glyphicon glyphicon-calendar\"></span> </span> </div> </div> </div> </div> <script type=\"text/javascript\"> $(function () { $('#datetimepicker6').datetimepicker(); $('#datetimepicker7').datetimepicker({ useCurrent: false //Important! See issue #1075 }); $(\"#datetimepicker6\").on(\"dp.change\", function (e) { $('#datetimepicker7').data(\"DateTimePicker\").minDate(e.date); }); $(\"#datetimepicker7\").on(\"dp.change\", function (e) { $('#datetimepicker6').data(\"DateTimePicker\").maxDate(e.date); }); }); </script>", "title": "Code"}, {"location": "#custom-icons", "text": "$(function () { $('#datetimepicker8').datetimepicker({ icons: { time: \"fa fa-clock-o\", date: \"fa fa-calendar\", up: \"fa fa-arrow-up\", down: \"fa fa-arrow-down\" } }); });", "title": "Custom Icons"}, {"location": "#code_7", "text": "<div class=\"container\"> <div class=\"col-sm-6\" style=\"height:130px;\"> <div class=\"form-group\"> <div class='input-group date' id='datetimepicker8'> <input type='text' class=\"form-control\" /> <span class=\"input-group-addon\"> <span class=\"fa fa-calendar\"> </span> </span> </div> </div> </div> <script type=\"text/javascript\"> $(function () { $('#datetimepicker8').datetimepicker({ icons: { time: \"fa fa-clock-o\", date: \"fa fa-calendar\", up: \"fa fa-arrow-up\", down: \"fa fa-arrow-down\" } }); }); </script> </div>", "title": "Code"}, {"location": "#view-mode", "text": "$(function () { $('#datetimepicker9').datetimepicker({ viewMode: 'years' }); });", "title": "View Mode"}, {"location": "#code_8", "text": "<div class=\"container\"> <div class=\"col-sm-6\" style=\"height:130px;\"> <div class=\"form-group\"> <div class='input-group date' id='datetimepicker9'> <input type='text' class=\"form-control\" /> <span class=\"input-group-addon\"> <span class=\"glyphicon glyphicon-calendar\"> </span> </span> </div> </div> </div> <script type=\"text/javascript\"> $(function () { $('#datetimepicker9').datetimepicker({ viewMode: 'years' }); }); </script> </div>", "title": "Code"}, {"location": "#min-view-mode", "text": "$(function () { $('#datetimepicker10').datetimepicker({ viewMode: 'years', format: 'MM/YYYY' }); });", "title": "Min View Mode"}, {"location": "#code_9", "text": "<div class=\"container\"> <div class=\"col-sm-6\" style=\"height:130px;\"> <div class=\"form-group\"> <div class='input-group date' id='datetimepicker10'> <input type='text' class=\"form-control\" /> <span class=\"input-group-addon\"> <span class=\"glyphicon glyphicon-calendar\"> </span> </span> </div> </div> </div> <script type=\"text/javascript\"> $(function () { $('#datetimepicker10').datetimepicker({ viewMode: 'years', format: 'MM/YYYY' }); }); </script> </div>", "title": "Code"}, {"location": "#disabled-days-of-the-week", "text": "$(function () { $('#datetimepicker11').datetimepicker({ daysOfWeekDisabled: [0, 6] }); });", "title": "Disabled Days of the Week"}, {"location": "#code_10", "text": "<div class=\"container\"> <div class=\"col-sm-6\" style=\"height:130px;\"> <div class=\"form-group\"> <div class='input-group date' id='datetimepicker11'> <input type='text' class=\"form-control\" /> <span class=\"input-group-addon\"> <span class=\"glyphicon glyphicon-calendar\"> </span> </span> </div> </div> </div> <script type=\"text/javascript\"> $(function () { $('#datetimepicker11').datetimepicker({ daysOfWeekDisabled: [0, 6] }); }); </script> </div>", "title": "Code"}, {"location": "#inline", "text": "$(function () { $('#datetimepicker12').datetimepicker({ inline: true, sideBySide: true }); });", "title": "Inline"}, {"location": "#code_11", "text": "<div style=\"overflow:hidden;\"> <div class=\"form-group\"> <div class=\"row\"> <div class=\"col-md-8\"> <div id=\"datetimepicker12\"></div> </div> </div> </div> <script type=\"text/javascript\"> $(function () { $('#datetimepicker12').datetimepicker({ inline: true, sideBySide: true }); }); </script> </div>", "title": "Code"}, {"location": "ChangeLog/", "text": "Version 4 4.17.42 Bug Squashing fixed moment dependencies to all be the same defaulted option.timeZone to '' instead of UTC. This way it will default to the local timezone if it's not set. fixed #959 fixed #1311 internal getMoment function no longer sets startOf('d') fixed #935 Other moved some (will move the rest soon) inline docs to JSDoc now that ReSharper supports it. moved getter/setter functions to options page instead. #1313 4.17.37 New Features Momentjs TZ intergration #1242 thanks @bodrick Independent CSS file, in case you don't want bootstrap for some reason Bug Squashing Slight changes decade view Moved all tooltip text to tooltips fixed #1212 4.15.35 New Features tooltips allows custom, localized text to be included for icon tooltips Bug Squashing fixed #1066 fixed #1087 sideBySide properly supports toolbarPlacement [top, bottom] fixed #1119 fixed #1069 added input.blur() fixed #1049 fixed doc example fixed #999 picker now looks for an element with .input-group-addon 4.14.30 New Features disabledTimeIntervals #644 allowInputToggle #929 focusOnShow #884 public viewDate function #872 enabledHours and disabledHours . dp.update fires when viewDate is changed (in most cases) #937 viewMode now supports a decades view. Note : because the year picker shows 12 years at a time, I've elected to make this view show blocks of 12 years Note : when selecting a decade the viewDate will change to the center of the selected years parseInputDate #1095 Bug Squashing fixed #815 by adding .wider when using both seconds and am/pm. fixed #816 changed both min/max date to move the selected date inside. fixed #855 #881 fillDate , fillMonths , fillDow uses startOf('day') , which will hopefully fix the DST issues. fixed #885 daysOfWeekDisabled will move the date to a valid date if useCurrent is true . Today button will check if the DoW is disabled. fixed #906 fixed #912 if useCurrent:false month and year view will no longer have the current month/year selected. fixed #914 use24hours will ignore anything in side of [] in the format string. fixed #916 added titles to all icons. At some point the text should be moved to the icon's array, but this would probably be a breaking change. fixed #940 added -1 tab index to am/pm selector Other Changes changed in/decrement behavior to check if the new date is valid at that granularity (hours, minutes, seconds). will also validate as before 4.7.14 Added several in new features: `keybinds`, `inline`, `debug`, `clear()`, `showClose`, `ingoreReadOnly`, `datepickerInput` and `keepInvalid`. Bug squashing 4.0.0 Changes for using the component Defined a Public API and hidden rest of functions, variables so that all configuration options can be changed dynamically. set/getDate() is now replaced with an overloaded date() function. Use it without a parameter to get the currently set date or with a parameter to set the date. hide() , show() , toggle() , enable() , disable() and the rest of setter functions now support chaining. ie $('#id').data('DateTimePicker').format('DD-MM-YYYY').minDate(moment()).defaultDate(moment()).show() works Replaced previous - next buttons in Date subviews with configurable icons Changed language option name to locale to be inline with moment naming Implemented #402 all data-date-* variables are more readable and also match with the ones in the configuration object options.direction and options.orientation were merged into a single object options.widgetPositioning with vertical and horizontal keys that take a string value of 'auto', 'top', 'bottom' and 'auto', 'left', 'right' respectively. Note that the 'up' option was renamed to 'top' Added functionality added a second way to define options as data attributes. Instead of adding distinct data-date-* config options you can now also pass a data-date-options attribute containing an object just the same as the options object that element.datetimepicker constructor call takes also added a options() public api function to get/set that takes an option object and applies it to the component in one call Implemented #130 by introducing a boolean options.calendarWeeks and calendarWeeks() api function Implemented #328 , #426 Implemented #432 . Widget DOM element is now lazily added only when shown and removed from the document when hidden. Implemented #141 and #283 Contributors related internal code changes Refactor all UI click functions and put them as functions in the actions array private variable Refactor template building process to seperate functions according to what they do Remove some styles that where hardcoded in the javascript code Refactor all code that changes the picker.date to change it through the setValue function to allow one place for validation logic (min/max/weekdaysenabled etc) and also one place for emmiting dp.change events The v4beta branch code includes all fixes up to v.3.1.2 Added toggle() to the public API which toggles the visibility of the DateTimePicker Refactored set function to be included in the setValue function Added a testing framework using jasmine and phantom.js Version 3 3.0.0 Fix for #170, #179, #183: Changed event to dp.- . This should fix the double change event firing. Fix for #192: setDate now fires dp.change Fix for #182: Picker will not set the default date if the input field has a value Fix for #169: Seconds doesn't get reset when changing the date (Thanks to PR #174) Fix for #168 z-index fix for BS modal Fix for #155 Picker properly displays the active year and month Fix for #154 CSS update to fix the collapse jump Fix for #150 and #75 minViewMode and viewMode work properly Fix for #147 AM/PM won't toggle when selecting a value from the hours grid Fix for #44 Finally! It's here!! Thanks to @ruiwei and his code on #210 picker will adjust the positioning of the widget. Manually merged PR PR #178 When using minuteStepping the minute select grid will only show available steppings PR #195, #197 Using the data-OPTION has been changed to data-date-OPTION . These options are expected to be on the input-group if you're using the input-group or the a bare input field if you're not using the input-group PR #184 The option sideBySide change be used to display both the d and the timepicker side by side PR #143 Added option daysOfWeekDisabled: [] . For example, use daysOfWeekDisabled: [0,6] to disable Sunday and Saturday Other Changes Changed picker width to 300px if using seconds and am/pm Added option useCurrent , thanks to @ruiwei. When true, picker will set the value to the current date/time (respects picker's format) Added option showToday , thanks to @ruiwei. When true, picker will display a small arrow to indicate today's date. Changed startDate to minDate and endDate to maxDate to make it more clear what these options do. Version 2 2.1.32 (Hotfix) Fix for #151: When a bad date value or the picker is cleared, the plugin will not longer attempt to reset it back to the previous date Fix for #140: setDate can be given null to force clear the picker 2.1.30 Important! build.less file name has been been changed to bootstrap-datetimepicker-build.less to prevent collisions Fix for #135: setStartDate and setEndDate should now properly set. Fix for #133: Typed in date now respects en/disabled dates Fix for #132: En/disable picker function works again Fix for #117, #119, #128, #121: double event change event issues should be fixed Fix for #112: change function no longer sets the input to a blank value if the passed in date is invalid Enhancement for #103: Increated the z-index of the widget 2.1.20 Fix for #83: Changes to the picker should fire native change event for knockout and the like as well as change.dp which contains the old date and the new date Fix for #78: Script has been update for breaking changes in Moment 2.4.0 Fix for #73: IE8 should be working now Enhancement for #79: minuteStepping option takes a number (default is 1). Changing the minutes in the time picker will step by this number. Enhancement for #74 and #65: useMinutes and useSeconds are now options. Disabling seconds will hide the seconds spinner. Disabling minutes will display 00 and hide the arrows Enhancement for #67: Picker will now attempt to convert all data-OPTION into its appropriate option 2.1.11 Fix for #51, #60 Fix for #52: Picker has its own moment object since moment 2.4.0 has removed global reference Fix for #57: New option for useStrict . When validating dates in update and change , the picker can use a stricter formatting validation Fix for #61: Picker should now properly take formatted date. Should also have correct start of the week for locales. Fix for #62: Default format will properly validate time picker only. 2.1.5 Custom icons, such as Font Awesome, are now supported. (#49) If more then one input-group-addon is present use datepickerbutton to identify where the picker should popup from. (#48) New Event: error.dp . Fires when Moment cannot parse the date or when the timepicker cannot change because of a disabledDates setting. Returns a Moment date object. The specific error can be found be using invalidAt() . For more information see Moment's docs Fix for #42, plugin will now check for A or a in the format string to determine if the AM/PM selector should display. Fix for #45, fixed null/empty and invalid dates Fix for #46, fixed active date highlighting Fix for #47, change.dp event to also include the previous date. 2.0.1 New event error.dp fires when plugin cannot parse date or when increase/descreasing hours/minutes to a disabled date. Minor fixes 2.0.0 disabledDates is now an option to set the disabled dates. It accepts date objects like new Date(\"November 12, 2013 00:00:00\") and 12/25/2013' and moment` date objects Events are easier to use", "title": "Version 4"}, {"location": "ChangeLog/#version-4", "text": "", "title": "Version 4"}, {"location": "ChangeLog/#41742", "text": "", "title": "4.17.42"}, {"location": "ChangeLog/#bug-squashing", "text": "fixed moment dependencies to all be the same defaulted option.timeZone to '' instead of UTC. This way it will default to the local timezone if it's not set. fixed #959 fixed #1311 internal getMoment function no longer sets startOf('d') fixed #935", "title": "<PERSON><PERSON>"}, {"location": "ChangeLog/#other", "text": "moved some (will move the rest soon) inline docs to JSDoc now that ReSharper supports it. moved getter/setter functions to options page instead. #1313", "title": "Other"}, {"location": "ChangeLog/#41737", "text": "", "title": "4.17.37"}, {"location": "ChangeLog/#new-features", "text": "Momentjs TZ intergration #1242 thanks @bodrick Independent CSS file, in case you don't want bootstrap for some reason", "title": "New Features"}, {"location": "ChangeLog/#bug-squashing_1", "text": "Slight changes decade view Moved all tooltip text to tooltips fixed #1212", "title": "<PERSON><PERSON>"}, {"location": "ChangeLog/#41535", "text": "", "title": "4.15.35"}, {"location": "ChangeLog/#new-features_1", "text": "tooltips allows custom, localized text to be included for icon tooltips", "title": "New Features"}, {"location": "ChangeLog/#bug-squashing_2", "text": "fixed #1066 fixed #1087 sideBySide properly supports toolbarPlacement [top, bottom] fixed #1119 fixed #1069 added input.blur() fixed #1049 fixed doc example fixed #999 picker now looks for an element with .input-group-addon", "title": "<PERSON><PERSON>"}, {"location": "ChangeLog/#41430", "text": "", "title": "4.14.30"}, {"location": "ChangeLog/#new-features_2", "text": "disabledTimeIntervals #644 allowInputToggle #929 focusOnShow #884 public viewDate function #872 enabledHours and disabledHours . dp.update fires when viewDate is changed (in most cases) #937 viewMode now supports a decades view. Note : because the year picker shows 12 years at a time, I've elected to make this view show blocks of 12 years Note : when selecting a decade the viewDate will change to the center of the selected years parseInputDate #1095", "title": "New Features"}, {"location": "ChangeLog/#bug-squashing_3", "text": "fixed #815 by adding .wider when using both seconds and am/pm. fixed #816 changed both min/max date to move the selected date inside. fixed #855 #881 fillDate , fillMonths , fillDow uses startOf('day') , which will hopefully fix the DST issues. fixed #885 daysOfWeekDisabled will move the date to a valid date if useCurrent is true . Today button will check if the DoW is disabled. fixed #906 fixed #912 if useCurrent:false month and year view will no longer have the current month/year selected. fixed #914 use24hours will ignore anything in side of [] in the format string. fixed #916 added titles to all icons. At some point the text should be moved to the icon's array, but this would probably be a breaking change. fixed #940 added -1 tab index to am/pm selector", "title": "<PERSON><PERSON>"}, {"location": "ChangeLog/#other-changes", "text": "changed in/decrement behavior to check if the new date is valid at that granularity (hours, minutes, seconds). will also validate as before", "title": "Other Changes"}, {"location": "ChangeLog/#4714", "text": "Added several in new features: `keybinds`, `inline`, `debug`, `clear()`, `showClose`, `ingoreReadOnly`, `datepickerInput` and `keepInvalid`. Bug squashing", "title": "4.7.14"}, {"location": "ChangeLog/#400", "text": "", "title": "4.0.0"}, {"location": "ChangeLog/#changes-for-using-the-component", "text": "Defined a Public API and hidden rest of functions, variables so that all configuration options can be changed dynamically. set/getDate() is now replaced with an overloaded date() function. Use it without a parameter to get the currently set date or with a parameter to set the date. hide() , show() , toggle() , enable() , disable() and the rest of setter functions now support chaining. ie $('#id').data('DateTimePicker').format('DD-MM-YYYY').minDate(moment()).defaultDate(moment()).show() works Replaced previous - next buttons in Date subviews with configurable icons Changed language option name to locale to be inline with moment naming Implemented #402 all data-date-* variables are more readable and also match with the ones in the configuration object options.direction and options.orientation were merged into a single object options.widgetPositioning with vertical and horizontal keys that take a string value of 'auto', 'top', 'bottom' and 'auto', 'left', 'right' respectively. Note that the 'up' option was renamed to 'top'", "title": "Changes for using the component"}, {"location": "ChangeLog/#added-functionality", "text": "added a second way to define options as data attributes. Instead of adding distinct data-date-* config options you can now also pass a data-date-options attribute containing an object just the same as the options object that element.datetimepicker constructor call takes also added a options() public api function to get/set that takes an option object and applies it to the component in one call Implemented #130 by introducing a boolean options.calendarWeeks and calendarWeeks() api function Implemented #328 , #426 Implemented #432 . Widget DOM element is now lazily added only when shown and removed from the document when hidden. Implemented #141 and #283", "title": "Added functionality"}, {"location": "ChangeLog/#contributors-related-internal-code-changes", "text": "Refactor all UI click functions and put them as functions in the actions array private variable Refactor template building process to seperate functions according to what they do Remove some styles that where hardcoded in the javascript code Refactor all code that changes the picker.date to change it through the setValue function to allow one place for validation logic (min/max/weekdaysenabled etc) and also one place for emmiting dp.change events The v4beta branch code includes all fixes up to v.3.1.2 Added toggle() to the public API which toggles the visibility of the DateTimePicker Refactored set function to be included in the setValue function Added a testing framework using jasmine and phantom.js", "title": "Contributors related internal code changes"}, {"location": "ChangeLog/#version-3", "text": "", "title": "Version 3"}, {"location": "ChangeLog/#300", "text": "Fix for #170, #179, #183: Changed event to dp.- . This should fix the double change event firing. Fix for #192: setDate now fires dp.change Fix for #182: Pick<PERSON> will not set the default date if the input field has a value Fix for #169: Seconds doesn't get reset when changing the date (Thanks to PR #174) Fix for #168 z-index fix for BS modal Fix for #155 Picker properly displays the active year and month Fix for #154 CSS update to fix the collapse jump Fix for #150 and #75 minViewMode and viewMode work properly Fix for #147 AM/PM won't toggle when selecting a value from the hours grid Fix for #44 Finally! It's here!! Thanks to @r<PERSON><PERSON> and his code on #210 picker will adjust the positioning of the widget.", "title": "3.0.0"}, {"location": "ChangeLog/#manually-merged-pr", "text": "PR #178 When using minuteStepping the minute select grid will only show available steppings PR #195, #197 Using the data-OPTION has been changed to data-date-OPTION . These options are expected to be on the input-group if you're using the input-group or the a bare input field if you're not using the input-group PR #184 The option sideBySide change be used to display both the d and the timepicker side by side PR #143 Added option daysOfWeekDisabled: [] . For example, use daysOfWeekDisabled: [0,6] to disable Sunday and Saturday", "title": "Manually merged PR"}, {"location": "ChangeLog/#other-changes_1", "text": "Changed picker width to 300px if using seconds and am/pm Added option useCurrent , thanks to @ruiwei. When true, picker will set the value to the current date/time (respects picker's format) Added option showToday , thanks to @ruiwei. When true, picker will display a small arrow to indicate today's date. Changed startDate to minDate and endDate to maxDate to make it more clear what these options do.", "title": "Other Changes"}, {"location": "ChangeLog/#version-2", "text": "", "title": "Version 2"}, {"location": "ChangeLog/#2132-hotfix", "text": "Fix for #151: When a bad date value or the picker is cleared, the plugin will not longer attempt to reset it back to the previous date Fix for #140: setDate can be given null to force clear the picker", "title": "2.1.32 (Hotfix)"}, {"location": "ChangeLog/#2130", "text": "", "title": "2.1.30"}, {"location": "ChangeLog/#important-buildless-file-name-has-been-been-changed-to-bootstrap-datetimepicker-buildless-to-prevent-collisions", "text": "Fix for #135: setStartDate and setEndDate should now properly set. Fix for #133: Typed in date now respects en/disabled dates Fix for #132: En/disable picker function works again Fix for #117, #119, #128, #121: double event change event issues should be fixed Fix for #112: change function no longer sets the input to a blank value if the passed in date is invalid Enhancement for #103: Increated the z-index of the widget", "title": "Important! build.less file name has been been changed to bootstrap-datetimepicker-build.less to prevent collisions"}, {"location": "ChangeLog/#2120", "text": "Fix for #83: Changes to the picker should fire native change event for knockout and the like as well as change.dp which contains the old date and the new date Fix for #78: <PERSON><PERSON><PERSON> has been update for breaking changes in Moment 2.4.0 Fix for #73: IE8 should be working now Enhancement for #79: minuteStepping option takes a number (default is 1). Changing the minutes in the time picker will step by this number. Enhancement for #74 and #65: useMinutes and useSeconds are now options. Disabling seconds will hide the seconds spinner. Disabling minutes will display 00 and hide the arrows Enhancement for #67: Picker will now attempt to convert all data-OPTION into its appropriate option", "title": "2.1.20"}, {"location": "ChangeLog/#2111", "text": "Fix for #51, #60 Fix for #52: Picker has its own moment object since moment 2.4.0 has removed global reference Fix for #57: New option for useStrict . When validating dates in update and change , the picker can use a stricter formatting validation Fix for #61: Picker should now properly take formatted date. Should also have correct start of the week for locales. Fix for #62: Default format will properly validate time picker only.", "title": "2.1.11"}, {"location": "ChangeLog/#215", "text": "Custom icons, such as Font Awesome, are now supported. (#49) If more then one input-group-addon is present use datepickerbutton to identify where the picker should popup from. (#48) New Event: error.dp . Fires when Moment cannot parse the date or when the timepicker cannot change because of a disabledDates setting. Returns a Moment date object. The specific error can be found be using invalidAt() . For more information see Moment's docs Fix for #42, plugin will now check for A or a in the format string to determine if the AM/PM selector should display. Fix for #45, fixed null/empty and invalid dates Fix for #46, fixed active date highlighting Fix for #47, change.dp event to also include the previous date.", "title": "2.1.5"}, {"location": "ChangeLog/#201", "text": "New event error.dp fires when plugin cannot parse date or when increase/descreasing hours/minutes to a disabled date. Minor fixes", "title": "2.0.1"}, {"location": "ChangeLog/#200", "text": "disabledDates is now an option to set the disabled dates. It accepts date objects like new Date(\"November 12, 2013 00:00:00\") and 12/25/2013' and moment` date objects Events are easier to use", "title": "2.0.0"}, {"location": "ContributorsGuide/", "text": "This guide is aimed to contributors wishing to understand the internals of the code in order to change/evolve the component. Note: this guide refers to version 4 which is currently in beta and will be updated as we progress Introduction This component consists actually of 2 subcomponent UI widgets one for the date and one for the time selection process. The developers can configure which of those are needed and also the granularity that the component will allow the users to select a date/time. Developers also choose the format that the selected datetime will be displayed in the input field. The component uses on jQuery , moment.js and bootstrap libraries. Code Private variables element - Holds the DOM element this instance is attached to options - Holds an object with the curently set options for the specific instance of the component. Don't directly change the properties of that object use the public API methods instead. DO NOT expose this object or its properties outside of the component. picker - Reference variable to the created instance (this) date - Holds the moment object for the model value of the component. DON'T directly change this variable unless you REALLY know what you are doing. Use setValue() function to set it. It handles all component logic for updating the model value and emitting all the appropriate events viewDate - Holds the currently selected value that the user has selected through the widget. This is not the model value this is the view value. Changing this usually requires a subsequent call to update() function unset - A boolean variable that holds wheather the components model value is set or not. Model's value starts as unset = true and if is either set by the user or programmatically through the api to a valid value then it is set to false . If subsequent events lead to an invalid value then this variable is set to true again. Setting this variable usually takes place in the setValue() function. input - Hold the DOM input element this instance is attached to component - Holds a reference to the .input-group DOM element that the widget is attached or false if it is attached directly on an input field widget - Holds a reference to the DOM element containing the widget or false if the widget is hidden use24hours - Holds whether the component uses 24 hours format or not. This is initialized on the format() function minViewModeNumber - Holds the Numeric equivelant of the options.minViewMode parameter format - Holds the current format string that is used for formating the date model value. Note this is not the same thing as the options.format as the second could be set to false in which case the first takes the locale's L or LT value currentViewMode - Hold the state of the current viewMode for the DatePicker subcomponent actions - An object containing all the functions that can be called when the users clicks on the widget datePickerModes - An array of objects with configuration parameters for the different views of the DatePicker subcomponent viewModes - An array of strings containing all the possible strings that options.viewMode can take through viewMode() public api function directionModes - An array of strings containing all the possible strings that options.direction can take through direction() public api function orientationModes - An array of strings containing all the possible strings that options.orientation can take through orientation() public api function Private functions Widget related getDatePickerTemplate() - returns a string containing the html code for the date picker subcomponent getTimePickerTemplate() - returns a string containing the html code for the time picker subcomponent getTemplate() - returns a string with containing the html code for all the DateTimePicker component place() - handles the placement of the widget's dropdown updateMonths() - updates the html subpage related to the months for Date picker view updateYears() - updates the html subpage related to the years for Date picker view fillDate() - updates the html subpage related to the days for Date picker view fillHours() - Creates the hours spans for the hours subview of the Time subcomponent fillMinutes() - Creates the minutes spans for the hours subview of the Time subcomponent fillSeconds() - Creates the seconds spans for the hours subview of the Time subcomponent fillTime() - Creates the main subview of the Time subcomponent update() - updates the UI of part of the widget fillDow() - Creates the day names in the days subview on the Date subcomponent fillMonths() - Creates the month spans for the months subview of the Date subcomponent createWidget() - creates the UI widget end attaches widget event listeners destroyWidget() - destroys the UI widget DOM element and detaches widget event listeners showMode(dir) - toggles between the various subpage related views of the DateTimePicker Events related notifyEvent(e) - Use this function when you want to send en event to listener this could be used as a filter later stopEvent(e) - Shortcut for stopping propagation of events doAction(e) - Proxy function to call all the UI related click events keydown(e) - Function to trap change(e) - Listener function to track change events occuring on the input dom element the component is attached to attachDatePickerElementEvents() - Attaches listeners to the existing DOM elements the component is attached to. Called upon construction of each datetimepicker instance detachDatePickerElementEvents() - Detaches listeners from the DOM element the component is attached to. Called on destroy() attachDatePickerWidgetEvents() - Attaches listeners on the components widget. Called on show() detachDatePickerWidgetEvents() - Detaches listeners on the components widget. Called on hide() Model related setValue(targetMoment) - Sets the model value of the component takes a moment object. An error event will be emmited if the targetMoment does not pass the configured validations. Otherwise the date variable will be set and the relevant events will be fired. isValid(targetMoment, granularity) - returns true if the targetMoment moment object is valid according to the components set validation rules ( min/maxDates , disabled/enabledDates and daysOfWeekDisabled ). You may pass a second variable to check only up the the specific granularity year, month, day, hour, minute, second Utilities indexGivenDates (givenDatesArray) - Function that takes the array from enabledDates() and disabledDates() public functions and stores them as object keys to enable quick lookup isInEnableDates(date) - Checks whether if the given moment object exists in the options.enabledDates object isInDisableDates(date) - Checks whether if the given moment object exists in the options.disabledDates array dataToOptions() - Parses data-date-* options set on the input dom element the component is attached to and returns an object with them isInFixed() - Checks if the dom element or its parents has a fixed position css rule. parseInputDate(date) - Parses a date parameter with moment using the component's options.format and options.useStrict . It returns a moment object or false if parsedMoment#isValid() returns false . Use this to parse date inputs from outside the component (public API calls). init() - Initializes the component. Called when the component instance is created", "title": "Dev Guide"}, {"location": "ContributorsGuide/#introduction", "text": "This component consists actually of 2 subcomponent UI widgets one for the date and one for the time selection process. The developers can configure which of those are needed and also the granularity that the component will allow the users to select a date/time. Developers also choose the format that the selected datetime will be displayed in the input field. The component uses on jQuery , moment.js and bootstrap libraries.", "title": "Introduction"}, {"location": "ContributorsGuide/#code", "text": "", "title": "Code"}, {"location": "ContributorsGuide/#private-variables", "text": "element - Holds the DOM element this instance is attached to options - Holds an object with the curently set options for the specific instance of the component. Don't directly change the properties of that object use the public API methods instead. DO NOT expose this object or its properties outside of the component. picker - Reference variable to the created instance (this) date - Holds the moment object for the model value of the component. DON'T directly change this variable unless you REALLY know what you are doing. Use setValue() function to set it. It handles all component logic for updating the model value and emitting all the appropriate events viewDate - Holds the currently selected value that the user has selected through the widget. This is not the model value this is the view value. Changing this usually requires a subsequent call to update() function unset - A boolean variable that holds wheather the components model value is set or not. Model's value starts as unset = true and if is either set by the user or programmatically through the api to a valid value then it is set to false . If subsequent events lead to an invalid value then this variable is set to true again. Setting this variable usually takes place in the setValue() function. input - Hold the DOM input element this instance is attached to component - Holds a reference to the .input-group DOM element that the widget is attached or false if it is attached directly on an input field widget - Holds a reference to the DOM element containing the widget or false if the widget is hidden use24hours - Holds whether the component uses 24 hours format or not. This is initialized on the format() function minViewModeNumber - Holds the Numeric equivelant of the options.minViewMode parameter format - Holds the current format string that is used for formating the date model value. Note this is not the same thing as the options.format as the second could be set to false in which case the first takes the locale's L or LT value currentViewMode - Hold the state of the current viewMode for the DatePicker subcomponent actions - An object containing all the functions that can be called when the users clicks on the widget datePickerModes - An array of objects with configuration parameters for the different views of the DatePicker subcomponent viewModes - An array of strings containing all the possible strings that options.viewMode can take through viewMode() public api function directionModes - An array of strings containing all the possible strings that options.direction can take through direction() public api function orientationModes - An array of strings containing all the possible strings that options.orientation can take through orientation() public api function", "title": "Private variables"}, {"location": "ContributorsGuide/#private-functions", "text": "", "title": "Private functions"}, {"location": "ContributorsGuide/#widget-related", "text": "getDatePickerTemplate() - returns a string containing the html code for the date picker subcomponent getTimePickerTemplate() - returns a string containing the html code for the time picker subcomponent getTemplate() - returns a string with containing the html code for all the DateTimePicker component place() - handles the placement of the widget's dropdown updateMonths() - updates the html subpage related to the months for Date picker view updateYears() - updates the html subpage related to the years for Date picker view fillDate() - updates the html subpage related to the days for Date picker view fillHours() - Creates the hours spans for the hours subview of the Time subcomponent fillMinutes() - Creates the minutes spans for the hours subview of the Time subcomponent fillSeconds() - Creates the seconds spans for the hours subview of the Time subcomponent fillTime() - Creates the main subview of the Time subcomponent update() - updates the UI of part of the widget fillDow() - Creates the day names in the days subview on the Date subcomponent fillMonths() - Creates the month spans for the months subview of the Date subcomponent createWidget() - creates the UI widget end attaches widget event listeners destroyWidget() - destroys the UI widget DOM element and detaches widget event listeners showMode(dir) - toggles between the various subpage related views of the DateTimePicker", "title": "Widget related"}, {"location": "ContributorsGuide/#events-related", "text": "notifyEvent(e) - Use this function when you want to send en event to listener this could be used as a filter later stopEvent(e) - Shortcut for stopping propagation of events doAction(e) - Proxy function to call all the UI related click events keydown(e) - Function to trap change(e) - Listener function to track change events occuring on the input dom element the component is attached to attachDatePickerElementEvents() - Attaches listeners to the existing DOM elements the component is attached to. Called upon construction of each datetimepicker instance detachDatePickerElementEvents() - Detaches listeners from the DOM element the component is attached to. Called on destroy() attachDatePickerWidgetEvents() - Attaches listeners on the components widget. Called on show() detachDatePickerWidgetEvents() - Detaches listeners on the components widget. Called on hide()", "title": "Events related"}, {"location": "ContributorsGuide/#model-related", "text": "setValue(targetMoment) - Sets the model value of the component takes a moment object. An error event will be emmited if the targetMoment does not pass the configured validations. Otherwise the date variable will be set and the relevant events will be fired. isValid(targetMoment, granularity) - returns true if the targetMoment moment object is valid according to the components set validation rules ( min/maxDates , disabled/enabledDates and daysOfWeekDisabled ). You may pass a second variable to check only up the the specific granularity year, month, day, hour, minute, second", "title": "Model related"}, {"location": "ContributorsGuide/#utilities", "text": "indexGivenDates (givenDatesArray) - Function that takes the array from enabledDates() and disabledDates() public functions and stores them as object keys to enable quick lookup isInEnableDates(date) - Checks whether if the given moment object exists in the options.enabledDates object isInDisableDates(date) - Checks whether if the given moment object exists in the options.disabledDates array dataToOptions() - Parses data-date-* options set on the input dom element the component is attached to and returns an object with them isInFixed() - Checks if the dom element or its parents has a fixed position css rule. parseInputDate(date) - Parses a date parameter with moment using the component's options.format and options.useStrict . It returns a moment object or false if parsedMoment#isValid() returns false . Use this to parse date inputs from outside the component (public API calls). init() - Initializes the component. Called when the component instance is created", "title": "Utilities"}, {"location": "Events/", "text": "Events dp.hide Fired when the widget is hidden. Parameters: e = { date //the currently set date. Type: moment object (clone) } Emitted from: toggle() hide() disable() dp.show Fired when the widget is shown. Parameters: No parameters are include, listen to dp.change instead Emitted from: toggle() show() dp.change Fired when the date is changed, including when changed to a non-date (e.g. When keepInvalid=true). Parameters: e = { date, //date the picker changed to. Type: moment object (clone) oldDate //previous date. Type: moment object (clone) or false in the event of a null } Emitted from: toggle() Note : Only fired when using useCurrent show() Note : Only fired when using useCurrent or when or the date is changed to comply with date rules (min/max etc) date(newDate) minDate(minDate) maxDate(maxDate) daysOfWeekDisabled() dp.error Fired when a selected date fails to pass validation. Parameters: e = { date //the invalid date. Type: moment object (clone) oldDate //previous date. Type: moment object (clone) or false in the event of a null } Emmited from: minDate(minDate) maxDate(maxDate) daysOfWeekDisabled() setValue() private function dp.update 4.14.30 Fired (in most cases) when the viewDate changes. E.g. Next and Previous buttons, selecting a year. Parameters: e = { change, //Change type as a momentjs format token. Type: string e.g. yyyy on year change viewDate //new viewDate. Type: moment object }", "title": "Events"}, {"location": "Events/#events", "text": "", "title": "Events"}, {"location": "Events/#dphide", "text": "Fired when the widget is hidden. Parameters: e = { date //the currently set date. Type: moment object (clone) } Emitted from: toggle() hide() disable()", "title": "dp.hide"}, {"location": "Events/#dpshow", "text": "Fired when the widget is shown. Parameters: No parameters are include, listen to dp.change instead Emitted from: toggle() show()", "title": "dp.show"}, {"location": "Events/#dpchange", "text": "Fired when the date is changed, including when changed to a non-date (e.g. When keepInvalid=true). Parameters: e = { date, //date the picker changed to. Type: moment object (clone) oldDate //previous date. Type: moment object (clone) or false in the event of a null } Emitted from: toggle() Note : Only fired when using useCurrent show() Note : Only fired when using useCurrent or when or the date is changed to comply with date rules (min/max etc) date(newDate) minDate(minDate) maxDate(maxDate) daysOfWeekDisabled()", "title": "dp.change"}, {"location": "Events/#dperror", "text": "Fired when a selected date fails to pass validation. Parameters: e = { date //the invalid date. Type: moment object (clone) oldDate //previous date. Type: moment object (clone) or false in the event of a null } Emmited from: minDate(minDate) maxDate(maxDate) daysOfWeekDisabled() setValue() private function", "title": "dp.error"}, {"location": "Events/#dpupdate", "text": "4.14.30 Fired (in most cases) when the viewDate changes. E.g. Next and Previous buttons, selecting a year. Parameters: e = { change, //Change type as a momentjs format token. Type: string e.g. yyyy on year change viewDate //new viewDate. Type: moment object }", "title": "dp.update"}, {"location": "Extras/", "text": "Extras Guides for making the picker work better with rails, IE, etc Rails 3 by <PERSON><PERSON><PERSON><PERSON> You can easily override the default rails form helpers ( date_select and datetime_select ) with bootstrap-datetimepicker for a much nicer experience. # Add to config/initializers/form.rb or the end of app/helpers/application_helper.rb module ActionView module Helpers class FormBuilder def date_select(method, options = {}, html_options = {}) existing_date = @object.send(method) formatted_date = existing_date.to_date.strftime(\"%F\") if existing_date.present? @template.content_tag(:div, :class => \"input-group\") do text_field(method, :value => formatted_date, :class => \"form-control datepicker\", :\"data-date-format\" => \"YYYY-MM-DD\") + @template.content_tag(:span, @template.content_tag(:span, \"\", :class => \"glyphicon glyphicon-calendar\") ,:class => \"input-group-addon\") end end def datetime_select(method, options = {}, html_options = {}) existing_time = @object.send(method) formatted_time = existing_time.to_time.strftime(\"%F %I:%M %p\") if existing_time.present? @template.content_tag(:div, :class => \"input-group\") do text_field(method, :value => formatted_time, :class => \"form-control datetimepicker\", :\"data-date-format\" => \"YYYY-MM-DD hh:mm A\") + @template.content_tag(:span, @template.content_tag(:span, \"\", :class => \"glyphicon glyphicon-calendar\") ,:class => \"input-group-addon\") end end end end end The time format used here is ActiveRecord-friendly, which means it will be parsed correctly when passed in through params to your record. That's all there is to it! Now all of your forms that use datetime_select or date_select will be automatically updated: <% form_for @post do |f| %> <div class=\"form-group\"> <label>Published At</label> <%= f.datetime_select :published_at %> </div> <% end %> IE 7 by EquilibriumCST I succeed to run this widget under IE7. Here is what I did. gliphicons are not working under IE7 so add this css file . And this enables the icons. Z-index problem with IE 7. I added position: relative and z-index: 10 to the parent container. Otherwise popup is shown under the next elements. JS events were not working well. If you open the datetimepicker widget and click on some button or date inside it, widget is automatically closed. So I added debug: true as an option when initializing the widget. Why I did this? I saw on line 1121 from bootsrap-datetimepicker.js the code 'blur': options.debug ? '' : hide . And now widget window is not closed on every click inside it, but now you can't close it anyway :) And closing should be done manually. I've added this document click handler. If you click something outside the widget, now closing works. $(document).click(function(e){ var target = $(e.target); if(target.parents('.bootstrap-datetimepicker-widget').length < 1 && !target.hasClass('datetimepickerInput') && !target.hasClass('datepickerIcon') && !target.hasClass('clockpickerIcon')){ if($('.bootstrap-datetimepicker-widget').length > 0){ $('#startDate').data('DateTimePicker').hide(); $('#startTime').data('DateTimePicker').hide(); $('.datetimepickerInput').blur(); } } }); But if you have more than one widget on the page like I did, clicking on one widget does'n close the other. Added below lines and now all works fine. $('#widget1').on(\"dp.show\",function (e) { $('#widget2).data('DateTimePicker').hide(); }); $('#widget2').on(\"dp.show\",function (e) { $('#widget1).data('DateTimePicker').hide(); }); I hope this will help to the others who are fighting with the old IE versions :)", "title": "Extras"}, {"location": "Extras/#extras", "text": "Guides for making the picker work better with rails, IE, etc", "title": "Extras"}, {"location": "Extras/#rails-3", "text": "by d<PERSON><PERSON>an You can easily override the default rails form helpers ( date_select and datetime_select ) with bootstrap-datetimepicker for a much nicer experience. # Add to config/initializers/form.rb or the end of app/helpers/application_helper.rb module ActionView module Helpers class FormBuilder def date_select(method, options = {}, html_options = {}) existing_date = @object.send(method) formatted_date = existing_date.to_date.strftime(\"%F\") if existing_date.present? @template.content_tag(:div, :class => \"input-group\") do text_field(method, :value => formatted_date, :class => \"form-control datepicker\", :\"data-date-format\" => \"YYYY-MM-DD\") + @template.content_tag(:span, @template.content_tag(:span, \"\", :class => \"glyphicon glyphicon-calendar\") ,:class => \"input-group-addon\") end end def datetime_select(method, options = {}, html_options = {}) existing_time = @object.send(method) formatted_time = existing_time.to_time.strftime(\"%F %I:%M %p\") if existing_time.present? @template.content_tag(:div, :class => \"input-group\") do text_field(method, :value => formatted_time, :class => \"form-control datetimepicker\", :\"data-date-format\" => \"YYYY-MM-DD hh:mm A\") + @template.content_tag(:span, @template.content_tag(:span, \"\", :class => \"glyphicon glyphicon-calendar\") ,:class => \"input-group-addon\") end end end end end The time format used here is ActiveRecord-friendly, which means it will be parsed correctly when passed in through params to your record. That's all there is to it! Now all of your forms that use datetime_select or date_select will be automatically updated: <% form_for @post do |f| %> <div class=\"form-group\"> <label>Published At</label> <%= f.datetime_select :published_at %> </div> <% end %>", "title": "Rails 3"}, {"location": "Extras/#ie-7", "text": "by EquilibriumCST I succeed to run this widget under IE7. Here is what I did. gliphicons are not working under IE7 so add this css file . And this enables the icons. Z-index problem with IE 7. I added position: relative and z-index: 10 to the parent container. Otherwise popup is shown under the next elements. JS events were not working well. If you open the datetimepicker widget and click on some button or date inside it, widget is automatically closed. So I added debug: true as an option when initializing the widget. Why I did this? I saw on line 1121 from bootsrap-datetimepicker.js the code 'blur': options.debug ? '' : hide . And now widget window is not closed on every click inside it, but now you can't close it anyway :) And closing should be done manually. I've added this document click handler. If you click something outside the widget, now closing works. $(document).click(function(e){ var target = $(e.target); if(target.parents('.bootstrap-datetimepicker-widget').length < 1 && !target.hasClass('datetimepickerInput') && !target.hasClass('datepickerIcon') && !target.hasClass('clockpickerIcon')){ if($('.bootstrap-datetimepicker-widget').length > 0){ $('#startDate').data('DateTimePicker').hide(); $('#startTime').data('DateTimePicker').hide(); $('.datetimepickerInput').blur(); } } }); But if you have more than one widget on the page like I did, clicking on one widget does'n close the other. Added below lines and now all works fine. $('#widget1').on(\"dp.show\",function (e) { $('#widget2).data('DateTimePicker').hide(); }); $('#widget2').on(\"dp.show\",function (e) { $('#widget1).data('DateTimePicker').hide(); }); I hope this will help to the others who are fighting with the old IE versions :)", "title": "IE 7"}, {"location": "FAQ/", "text": "FAQs How do I disable the date or time element How do I format ...; How do I add seconds; etc. The picker uses the format option to decide what components to show. Set format to LT , LTS or another valid MomentJs format string to display certain components How do I change the language/locale The picker uses MomentJs to determine the language string. You can use moment-with-locales or you can include whatever local file you need. Set the picker's locale option to de or whatever the locale string is. How do I change the styles? The picker closes. Set debug:true which will force the picker to stay open, even onBlur . You can hide the picker manually by calling hide() How do I change the start of the week? Start of the week is based on the locale provided . This is defined by moment's locales. If you want to change it, create your own locale file or override. See moment's docs . How I use the picker as birthday picker? Use the viewMode option to 'years'", "title": "FAQs"}, {"location": "FAQ/#faqs", "text": "", "title": "FAQs"}, {"location": "FAQ/#how-do-i-disable-the-date-or-time-element", "text": "How do I format ...; How do I add seconds; etc. The picker uses the format option to decide what components to show. Set format to LT , LTS or another valid MomentJs format string to display certain components", "title": "How do I disable the date or time element"}, {"location": "FAQ/#how-do-i-change-the-languagelocale", "text": "The picker uses MomentJs to determine the language string. You can use moment-with-locales or you can include whatever local file you need. Set the picker's locale option to de or whatever the locale string is.", "title": "How do I change the language/locale"}, {"location": "FAQ/#how-do-i-change-the-styles-the-picker-closes", "text": "Set debug:true which will force the picker to stay open, even onBlur . You can hide the picker manually by calling hide()", "title": "How do I change the styles? The picker closes."}, {"location": "FAQ/#how-do-i-change-the-start-of-the-week", "text": "Start of the week is based on the locale provided . This is defined by moment's locales. If you want to change it, create your own locale file or override. See moment's docs .", "title": "How do I change the start of the week?"}, {"location": "FAQ/#how-i-use-the-picker-as-birthday-picker", "text": "Use the viewMode option to 'years'", "title": "How I use the picker as birthday picker?"}, {"location": "Functions/", "text": "Functions Note All functions are accessed via the data attribute e.g. $('#datetimepicker').data(\"DateTimePicker\").FUNCTION() destroy() Destroys the widget and removes all attached event listeners toggle() Shows or hides the widget Emits dp.hide - if the widget is hidden after the toggle call dp.show - if the widget is show after the toggle call dp.change - if the widget is opened for the first time and the input element is empty and options.useCurrent != false show() Shows the widget Emits dp.show - if the widget was hidden before that call dp.change - if the widget is opened for the first time and the useCurrent is set to true or to a granularity value and the input element the component is attached to has an empty value hide() Hides the widget Emits dp.hide - if the widget was visible before that call disable() Disables the input element, the component is attached to, by adding a disabled=\"true\" attribute to it. If the widget was visible before that call it is hidden. Emits dp.hide - if the widget was visible before that call enable() Enables the input element, the component is attached to, by removing disabled attribute from it. clear() Clears the datepicker by setting the value to null viewDate 4.14.30 Issue #872 viewDate() Returns a moment variable with the currently set options.viewDate option. viewDate(viewDate) Takes a string, moment or Date value. This will change the viewDate without changing or setting the selected date.", "title": "Functions"}, {"location": "Functions/#functions", "text": "Note All functions are accessed via the data attribute e.g. $('#datetimepicker').data(\"DateTimePicker\").FUNCTION()", "title": "Functions"}, {"location": "Functions/#destroy", "text": "Destroys the widget and removes all attached event listeners", "title": "destroy()"}, {"location": "Functions/#toggle", "text": "Shows or hides the widget", "title": "toggle()"}, {"location": "Functions/#emits", "text": "dp.hide - if the widget is hidden after the toggle call dp.show - if the widget is show after the toggle call dp.change - if the widget is opened for the first time and the input element is empty and options.useCurrent != false", "title": "Emits"}, {"location": "Functions/#show", "text": "Shows the widget", "title": "show()"}, {"location": "Functions/#emits_1", "text": "dp.show - if the widget was hidden before that call dp.change - if the widget is opened for the first time and the useCurrent is set to true or to a granularity value and the input element the component is attached to has an empty value", "title": "Emits"}, {"location": "Functions/#hide", "text": "Hides the widget", "title": "hide()"}, {"location": "Functions/#emits_2", "text": "dp.hide - if the widget was visible before that call", "title": "Emits"}, {"location": "Functions/#disable", "text": "Disables the input element, the component is attached to, by adding a disabled=\"true\" attribute to it. If the widget was visible before that call it is hidden.", "title": "disable()"}, {"location": "Functions/#emits_3", "text": "dp.hide - if the widget was visible before that call", "title": "Emits"}, {"location": "Functions/#enable", "text": "Enables the input element, the component is attached to, by removing disabled attribute from it.", "title": "enable()"}, {"location": "Functions/#clear", "text": "Clears the datepicker by setting the value to null", "title": "clear()"}, {"location": "Functions/#viewdate", "text": "4.14.30 Issue #872", "title": "viewDate"}, {"location": "Functions/#viewdate_1", "text": "Returns a moment variable with the currently set options.viewDate option.", "title": "viewDate()"}, {"location": "Functions/#viewdateviewdate", "text": "Takes a string, moment or Date value. This will change the viewDate without changing or setting the selected date.", "title": "viewDate(viewDate)"}, {"location": "Installing/", "text": "Minimal Requirements jQuery Moment.js Bootstrap.js (transition and collapse are required if you're not using the full Bootstrap) Bootstrap Datepicker script Bootstrap CSS Bootstrap Datepicker CSS Locales: Moment's locale files are here Installation Guides Bower Nuget Rails Angular Meteor.js Manual bower Run the following command: bower install eonasdan-bootstrap-datetimepicker#latest --save Include necessary scripts and styles: <head> <!-- ... --> <script type=\"text/javascript\" src=\"/bower_components/jquery/jquery.min.js\"></script> <script type=\"text/javascript\" src=\"/bower_components/moment/min/moment.min.js\"></script> <script type=\"text/javascript\" src=\"/bower_components/bootstrap/dist/js/bootstrap.min.js\"></script> <script type=\"text/javascript\" src=\"/bower_components/eonasdan-bootstrap-datetimepicker/build/js/bootstrap-datetimepicker.min.js\"></script> <link rel=\"stylesheet\" href=\"/bower_components/bootstrap/dist/css/bootstrap.min.css\" /> <link rel=\"stylesheet\" href=\"/bower_components/eonasdan-bootstrap-datetimepicker/build/css/bootstrap-datetimepicker.min.css\" /> </head> Nuget LESS : PM> Install-Package Bootstrap.v3.Datetimepicker CSS : PM> Install-Package Bootstrap.v3.Datetimepicker.CSS <head> <script type=\"text/javascript\" src=\"/scripts/jquery.min.js\"></script> <script type=\"text/javascript\" src=\"/scripts/moment.min.js\"></script> <script type=\"text/javascript\" src=\"/scripts/bootstrap.min.js\"></script> <script type=\"text/javascript\" src=\"/scripts/bootstrap-datetimepicker.*js\"></script> <!-- include your less or built css files --> <!-- bootstrap-datetimepicker-build.less will pull in \"../bootstrap/variables.less\" and \"bootstrap-datetimepicker.less\"; or <link rel=\"stylesheet\" href=\"/Content/bootstrap-datetimepicker.css\" /> --> </head> Rails Add the following to your Gemfile : gem 'momentjs-rails', '>= 2.9.0' gem 'bootstrap3-datetimepicker-rails', '~> 4.14.30' Note: You may need to change the version number above to the version number on the badge above. Read the rest of the install instructions @ TrevorS/bootstrap3-datetimepicker-rails Angular Wrapper Follow the link here Meteor.js This widget has been package for the Meteor.js platform, to install it use meteorite as follows: $ mrt add tsega:bootstrap3-datetimepicker For more detail see the package page on Atmosphere Manual Acquire jQuery Acquire Moment.js Bootstrap 3 collapse and transition plugins Make sure to include *.JS files for plugins collapse and transitions . They are included with bootstrap in js/ directory Alternatively you could include the whole bundle of bootstrap plugins from bootstrap.js <script type=\"text/javascript\" src=\"/path/to/jquery.js\"></script> <script type=\"text/javascript\" src=\"/path/to/moment.js\"></script> <script type=\"text/javascript\" src=\"/path/to/bootstrap/js/transition.js\"></script> <script type=\"text/javascript\" src=\"/path/to/bootstrap/js/collapse.js\"></script> <script type=\"text/javascript\" src=\"/path/to/bootstrap/dist/bootstrap.min.js\"></script> <script type=\"text/javascript\" src=\"/path/to/bootstrap-datetimepicker.min.js\"></script> Knockout ko.bindingHandlers.dateTimePicker = { init: function (element, valueAccessor, allBindingsAccessor) { //initialize datepicker with some optional options var options = allBindingsAccessor().dateTimePickerOptions || {}; $(element).datetimepicker(options); //when a user changes the date, update the view model ko.utils.registerEventHandler(element, \"dp.change\", function (event) { var value = valueAccessor(); if (ko.isObservable(value)) { if (event.date != null && !(event.date instanceof Date)) { value(event.date.toDate()); } else { value(event.date); } } }); ko.utils.domNodeDisposal.addDisposeCallback(element, function () { var picker = $(element).data(\"DateTimePicker\"); if (picker) { picker.destroy(); } }); }, update: function (element, valueAccessor, allBindings, viewModel, bindingContext) { var picker = $(element).data(\"DateTimePicker\"); //when the view model is updated, update the widget if (picker) { var koDate = ko.utils.unwrapObservable(valueAccessor()); //in case return from server datetime i am get in this form for example /Date(93989393)/ then fomat this koDate = (typeof (koDate) !== 'object') ? new Date(parseFloat(koDate.replace(/[^0-9]/g, ''))) : koDate; picker.date(koDate); } } }; CSS styles Using LESS @import \"/path/to/bootstrap/less/variables\"; @import \"/path/to/bootstrap-datetimepicker/src/less/bootstrap-datetimepicker-build.less\"; // [...] your custom styles and variables Using CSS (default color palette) <link rel=\"stylesheet\" href=\"/path/to/bootstrap-datetimepicker/build/css/bootstrap-datetimepicker.min.css\" />", "title": "Installing"}, {"location": "Installing/#minimal-requirements", "text": "jQuery Moment.js Bootstrap.js (transition and collapse are required if you're not using the full Bootstrap) Bootstrap Datepicker script Bootstrap CSS Bootstrap Datepicker CSS Locales: Moment's locale files are here", "title": "Minimal Requirements"}, {"location": "Installing/#installation-guides", "text": "Bower Nuget Rails Angular Meteor.js Manual", "title": "Installation Guides"}, {"location": "Installing/#bower", "text": "Run the following command: bower install eonasdan-bootstrap-datetimepicker#latest --save Include necessary scripts and styles: <head> <!-- ... --> <script type=\"text/javascript\" src=\"/bower_components/jquery/jquery.min.js\"></script> <script type=\"text/javascript\" src=\"/bower_components/moment/min/moment.min.js\"></script> <script type=\"text/javascript\" src=\"/bower_components/bootstrap/dist/js/bootstrap.min.js\"></script> <script type=\"text/javascript\" src=\"/bower_components/eonasdan-bootstrap-datetimepicker/build/js/bootstrap-datetimepicker.min.js\"></script> <link rel=\"stylesheet\" href=\"/bower_components/bootstrap/dist/css/bootstrap.min.css\" /> <link rel=\"stylesheet\" href=\"/bower_components/eonasdan-bootstrap-datetimepicker/build/css/bootstrap-datetimepicker.min.css\" /> </head>", "title": "bower"}, {"location": "Installing/#nuget", "text": "", "title": "Nuget"}, {"location": "Installing/#less", "text": "PM> Install-Package Bootstrap.v3.Datetimepicker", "title": "LESS:"}, {"location": "Installing/#css", "text": "PM> Install-Package Bootstrap.v3.Datetimepicker.CSS <head> <script type=\"text/javascript\" src=\"/scripts/jquery.min.js\"></script> <script type=\"text/javascript\" src=\"/scripts/moment.min.js\"></script> <script type=\"text/javascript\" src=\"/scripts/bootstrap.min.js\"></script> <script type=\"text/javascript\" src=\"/scripts/bootstrap-datetimepicker.*js\"></script> <!-- include your less or built css files --> <!-- bootstrap-datetimepicker-build.less will pull in \"../bootstrap/variables.less\" and \"bootstrap-datetimepicker.less\"; or <link rel=\"stylesheet\" href=\"/Content/bootstrap-datetimepicker.css\" /> --> </head>", "title": "CSS:"}, {"location": "Installing/#rails", "text": "Add the following to your Gemfile : gem 'momentjs-rails', '>= 2.9.0' gem 'bootstrap3-datetimepicker-rails', '~> 4.14.30' Note: You may need to change the version number above to the version number on the badge above. Read the rest of the install instructions @ TrevorS/bootstrap3-datetimepicker-rails", "title": "Rails"}, {"location": "Installing/#angular-wrapper", "text": "Follow the link here", "title": "Angular Wrapper"}, {"location": "Installing/#meteorjs", "text": "This widget has been package for the Meteor.js platform, to install it use meteorite as follows: $ mrt add tsega:bootstrap3-datetimepicker For more detail see the package page on Atmosphere", "title": "Meteor.js"}, {"location": "Installing/#manual", "text": "", "title": "Manual"}, {"location": "Installing/#acquire-jquery", "text": "", "title": "Acquire j<PERSON><PERSON>y"}, {"location": "Installing/#acquire-momentjs", "text": "", "title": "Acquire  Moment.js"}, {"location": "Installing/#bootstrap-3-collapse-and-transition-plugins", "text": "Make sure to include *.JS files for plugins collapse and transitions . They are included with bootstrap in js/ directory Alternatively you could include the whole bundle of bootstrap plugins from bootstrap.js <script type=\"text/javascript\" src=\"/path/to/jquery.js\"></script> <script type=\"text/javascript\" src=\"/path/to/moment.js\"></script> <script type=\"text/javascript\" src=\"/path/to/bootstrap/js/transition.js\"></script> <script type=\"text/javascript\" src=\"/path/to/bootstrap/js/collapse.js\"></script> <script type=\"text/javascript\" src=\"/path/to/bootstrap/dist/bootstrap.min.js\"></script> <script type=\"text/javascript\" src=\"/path/to/bootstrap-datetimepicker.min.js\"></script>", "title": "Bootstrap 3 collapse and transition plugins"}, {"location": "Installing/#knockout", "text": "ko.bindingHandlers.dateTimePicker = { init: function (element, valueAccessor, allBindingsAccessor) { //initialize datepicker with some optional options var options = allBindingsAccessor().dateTimePickerOptions || {}; $(element).datetimepicker(options); //when a user changes the date, update the view model ko.utils.registerEventHandler(element, \"dp.change\", function (event) { var value = valueAccessor(); if (ko.isObservable(value)) { if (event.date != null && !(event.date instanceof Date)) { value(event.date.toDate()); } else { value(event.date); } } }); ko.utils.domNodeDisposal.addDisposeCallback(element, function () { var picker = $(element).data(\"DateTimePicker\"); if (picker) { picker.destroy(); } }); }, update: function (element, valueAccessor, allBindings, viewModel, bindingContext) { var picker = $(element).data(\"DateTimePicker\"); //when the view model is updated, update the widget if (picker) { var koDate = ko.utils.unwrapObservable(valueAccessor()); //in case return from server datetime i am get in this form for example /Date(93989393)/ then fomat this koDate = (typeof (koDate) !== 'object') ? new Date(parseFloat(koDate.replace(/[^0-9]/g, ''))) : koDate; picker.date(koDate); } } };", "title": "Knockout"}, {"location": "Installing/#css-styles", "text": "", "title": "CSS styles"}, {"location": "Installing/#using-less", "text": "@import \"/path/to/bootstrap/less/variables\"; @import \"/path/to/bootstrap-datetimepicker/src/less/bootstrap-datetimepicker-build.less\"; // [...] your custom styles and variables Using CSS (default color palette) <link rel=\"stylesheet\" href=\"/path/to/bootstrap-datetimepicker/build/css/bootstrap-datetimepicker.min.css\" />", "title": "Using LESS"}, {"location": "Options/", "text": "Options Note All options are accessed via the data attribute e.g. $('#datetimepicker').data(\"DateTimePicker\").OPTION() options() Returns the components current options object. Note that the changing the values of the returned object does not change the components actual configuration. Use options(options) to set the components options massively or the other methods for setting config options individually. options([options]) Takes an object variable with option key:value properties and configures the component. Use this to update multiple options on the component. date Returns the component's model current date, a moment object or null if not set. date([newDate]) Takes string, Date, moment, null parameter and sets the components model current moment to it. Passing a null value unsets the components model current moment. Parsing of the newDate parameter is made using moment library with the options.format and options.useStrict components configuration. Throws TypeError - in case the newDate cannot be parsed Emits dp.change - In case newDate is different from current moment format Default: false See momentjs' docs for valid formats. Format also dictates what components are shown, e.g. MM/dd/YYYY will not display the time picker. format() Returns the component's options.format string format(format) Takes a moment.js format string and sets the components options.format . This is used for displaying and also for parsing input strings either from the input element the component is attached to or the date() function. The parameter can also be a boolean:false in which case the format is set to the locale's L LT . Note: this is also used to determine if the TimePicker sub component will display the hours in 12 or 24 format. (if 'a' or 'h' exists in the passed string then a 12 hour mode is set) dayViewHeaderFormat Default: 'MMMM YYYY' Changes the heading of the datepicker when in \"days\" view. dayViewHeaderFormat() Returns a string variable with the currently set options.dayViewHeaderFormat option. dayViewHeaderFormat(string) Takes a string value. Used to customize the header of the day view. extraFormats Default: false Allows for several input formats to be valid. See this PR . extraFormats() Returns a boolean or array with the options.extraFormats option configuration extraFormats(formats) Takes an array of valid input moment format options. stepping Default: 1 Number of minutes the up/down arrow's will move the minutes value in the time picker stepping() Returns a number with the options.stepping option configuration stepping(number) Takes a number . This be the amount the up/down arrows move the minute value with a time picker. min/maxDate Default: false Accepts: date, moment, string Prevents date/time selections before this date. Will override defaultDate and useCurrent if either of these settings are the same day since both options are invalid according to the rules you've selected. minDate() Returns the currently set moment of the options.minDate or false if not set minDate(minDate) Takes a minDate string, Date, moment, boolean:false parameter and disallows the user to select a moment that is before that moment. If a boolean:false value is passed the options.minDate parameter is cleared and there is no restriction to the miminum moment the user can select. Note: If the minDate parameter is after the currently selected moment the currently selected moment changes to minDate parameter Throws TypeError - if minDate parameter cannot be parsed using the options.format and options.useStrict configuration settings TypeError - if minDate parameter is after options.maxDate Emits dp.change - if the new minDate is after currently selected moment (waiting for #472 to close in order to finalize this part) dp.error - if the new minDate is after currently selected moment (waiting for #472 to close in order to finalize this part) maxDate() Returns the currently set moment of the options.maxDate or false if not set maxDate(maxDate) Takes a maxDate string, Date, moment, boolean:false parameter and disallows the user to select a moment that is after that moment. If a boolean:false value is passed options.maxDate is cleared and there is no restriction to the maximum moment the user can select. Note: If maxDate is before the currently selected moment the currently selected moment changes to maxDate Throws TypeError - if maxDate parameter cannot be parsed using the options.format and options.useStrict configuration settings TypeError - if maxDate parameter is before options.minDate Emits dp.change - if the new maxDate is after currently selected moment (waiting for #472 to close in order to finalize this part) dp.error - if the new maxDate is after currently selected moment (waiting for #472 to close in order to finalize this part) useCurrent Default: true On show , will set the picker to the current date/time. useCurrent() Returns a boolean or string with the options.useCurrent option configuration useCurrent(boolean or string) Takes a boolean or string . If a boolean true is passed and the components model moment is not set (either through setDate or through a valid value on the input element the component is attached to) then the first time the user opens the datetimepicker widget the value is initialized to the current moment of the action. If a false boolean is passed then no initialization happens on the input element. You can select the granularity on the initialized moment by passing one of the following strings ( 'year', 'month', 'day', 'hour', 'minute' ) in the variable. If for example you pass 'day' to the setUseCurrent function and the input field is empty the first time the user opens the datetimepicker widget the input text will be initialized to the current datetime with day granularity (ie if currentTime = 2014-08-10 13:32:33 the input value will be initialized to 2014-08-10 00:00:00 ) Note: If the options.defaultDate is set or the input element the component is attached to has already a value that takes precedence and the functionality of useCurrent is not triggered! collapse Default: true Using a Bootstraps collapse to switch between date/time pickers. collapse() Returns a boolean of the options.sideBySide . collapse(collapse) Takes a boolean . If set to false the picker will display similar to sideBySide except vertical. locale Default: moment.locale() Accepts: string, moment.locale('locale') See momentjs for valid locales. You must include moment-with-locales.js or a local js file. locale() Returns the currently set locale of the options.locale locale(newLocale) Takes a string of any valid moment locale e.g. de for German. Throws TypeError - if the locale is not loaded via a separate script or moment-with-locales defaultDate Default: false Accepts: date, moment, string Sets the picker default date/time. Overrides useCurrent defaultDate() Returns a moment with the options.defaultDate option configuration or false if not set defaultDate(defaultDate) Takes a string, Date, moment, boolean:false . Will set the picker's inital date. If a boolean:false value is passed the options.defaultDate parameter is cleared. TypeError - if the provided date pass validation, including disabledDates , enabledDates , minDate , maxDate , and daysOfWeekDisabled TypeError - if the provided date cannot be parsed by momentjs en/disabledDates Default: false Accepts: array of [date, moment, string] disabledDates() Returns an array with the currently set disabled dates on the component. disabledDates(dates) Takes an [ string or Date or moment ] of values and disallows the user to select those days. Setting this takes precedence over options.minDate , options.maxDate configuration. Also calling this function removes the configuration of options.enabledDates if such exist. Note: These values are matched with Day granularity. enabledDates() Returns an array with the currently set enabled dates on the component. enabledDates(dates) Takes an [ string or Date or moment ] of values and allows the user to select only from those days. Setting this takes precedence over options.minDate , options.maxDate configuration. Also calling this function removes the configuration of options.disabledDates if such exist. Note: These values are matched with Day granularity. icons Default: { time: 'glyphicon glyphicon-time', date: 'glyphicon glyphicon-calendar', up: 'glyphicon glyphicon-chevron-up', down: 'glyphicon glyphicon-chevron-down', previous: 'glyphicon glyphicon-chevron-left', next: 'glyphicon glyphicon-chevron-right', today: 'glyphicon glyphicon-screenshot', clear: 'glyphicon glyphicon-trash', close: 'glyphicon glyphicon-remove' } Accepts: object with all or some of the parameters above Change the default icons for the pickers functions. icons() Returns an Ojbect of options.icons icons(icons) Takes an Ojbect of strings . Throws TypeError - if icons parameter is not an Ojbect useStrict Default: false Defines if moment should use strict date parsing when considering a date to be valid. useStrict() Returns a boolean of the options.useStrict useStrict(useStrict) Takes a boolean . If useStrict is true , momentjs parsing rules will be stricter when determining if a date is valid or not. sideBySide Default: false Shows the picker side by side when using the time and date together. sideBySide() Returns a boolean of the options.sideBySide . sideBySide(sideBySide) Takes a boolean . If sideBySide is true and the time picker is used, both components will display side by side instead of collapsing. daysOfWeekDisabled Default: [] Accepts: array of numbers from 0-6 Disables the section of days of the week, e.g. weekends. daysOfWeekDisabled() Returns an array with the options.daysOfWeekDisabled configuration setting of the component. daysOfWeekDisabled(daysOfWeek) Takes an [ Number : 0 to 6 ] and disallow the user to select weekdays that exist in this array. This has lower priority over the options.minDate , options.maxDate , options.disabledDates and options.enabledDates configuration settings. Emits dp.change - if the currently selected moment falls in the values passed on the daysOfWeek parameter. (waiting for #472 to close in order to finalize this part) dp.error - if the currently selected moment falls in the values passed on the daysOfWeek parameter. (waiting for #472 to close in order to finalize this part) calendarWeeks Default: false Shows the week of the year to the left of first day of the week. calendarWeeks() Returns a boolean with the current options.calendarWeeks option configuration calendarWeeks(boolean) Takes a boolean variable to set if the week numbers will appear to the left on the days view viewMode Default: 'days' Accepts: 'decades','years','months','days' The default view to display when the picker is shown. Note : To limit the picker to selecting, for instance the year and month, use format: MM/YYYY . viewMode() Returns a string of the options.viewMode . viewMode(newViewMode) Takes a string . Valid values are 'days' , 'months' , 'years' and 'decades' Throws TypeError - if newViewMode parameter is not an a string or if newViewMode is not a valid value. toolbarPlacement Default: 'default' Accepts: 'default', 'top', 'bottom' Changes the placement of the icon toolbar. toolbarplacement() Returns a string variable with the currently set options.toolbarplacement option. toolbarplacement(string) Takes a string value. Valid values are 'default' , 'top' and 'bottom' . Changes the placement of the toolbar where the today, clear, component switch icon are located. showTodayButton Default: false Show the \"Today\" button in the icon toolbar. Clicking the \"Today\" button will set the calendar view and set the date to now . showTodayButton() Returns a boolean variable with the currently set options.showTodayButton option. showTodayButton(boolean) Takes a boolean variable to set if the Today button will appear on the widget showClear Default: false Show the \"Clear\" button in the icon toolbar. Clicking the \"Clear\" button will set the calendar to null. showClear() Returns a boolean variable with the currently set options.showClear option. showClear(boolean) Takes a boolean variable to set if the clear date button will appear on the widget showClose Default: false Show the \"Close\" button in the icon toolbar. Clicking the \"Close\" button will call hide() showClose() Returns a boolean variable with the currently set options.showClose option. showClose(boolean) Takes a boolean value. If true , an icon will be displayed on the toolbar that will hide the picker widgetPositioning Default: { horizontal: 'auto' vertical: 'auto' } Accepts: object with the all or one of the parameters above horizontal: 'auto', 'left', 'right' vertical: 'auto', 'top', 'bottom' widgetPositioning() Returns the currently set options.widgetPositioning object containing two keys horizontal and vertical widgetPositioning(positioningObject) Takes an object parameter that can contain two keys vertical and horizontal each having a value of 'auto', 'top', 'bottom' for vertical and 'auto', 'left', 'right' for horizontal which defines where the dropdown with the widget will appear relative to the input element the component is attached to. 'auto' is the default value for both horizontal and vertical keys and it tries to automatically place the dropdown in a position that is visible to the user. Usually you should not override those options unless you have a special need in your layout. widgetParent Default: null Accepts: string or jQuery object On picker show, places the widget at the identifier (string) or jQuery object if the element has css position: 'relative' widgetParent() Returns a $(element) variable with the currently set options.widgetParent option. widgetParent(widgetParent) Takes a string or $(element) value. keepOpen Default: false Will cause the date picker to stay open after selecting a date. keepOpen() Returns a boolean variable with the currently set options.keepOpen option. keepOpen(boolean) Takes a boolean value. inline Default: false Will display the picker inline without the need of a input field. This will also hide borders and shadows. inline() Returns a boolean variable with the currently set options.inline option. inline(boolean) Takes a boolean value. keepInvalid 4.7.14 Default: false Will cause the date picker to not revert or overwrite invalid dates. keepInvalid() Returns a string variable with the currently set options.keepInvalid option. keepInvalid(boolean) Takes a boolean value. If true , invalid dates will not be reverted to a previous selection or changed. keyBinds Default: up: function (widget) { if (widget.find('.datepicker').is(':visible')) { this.date(this.date().clone().subtract(7, 'd')); } else { this.date(this.date().clone().add(1, 'm')); } }, down: function (widget) { if (!widget) { this.show(); } else if (widget.find('.datepicker').is(':visible')) { this.date(this.date().clone().add(7, 'd')); } else { this.date(this.date().clone().subtract(1, 'm')); } }, 'control up': function (widget) { if (widget.find('.datepicker').is(':visible')) { this.date(this.date().clone().subtract(1, 'y')); } else { this.date(this.date().clone().add(1, 'h')); } }, 'control down': function (widget) { if (widget.find('.datepicker').is(':visible')) { this.date(this.date().clone().add(1, 'y')); } else { this.date(this.date().clone().subtract(1, 'h')); } }, left: function (widget) { if (widget.find('.datepicker').is(':visible')) { this.date(this.date().clone().subtract(1, 'd')); } }, right: function (widget) { if (widget.find('.datepicker').is(':visible')) { this.date(this.date().clone().add(1, 'd')); } }, pageUp: function (widget) { if (widget.find('.datepicker').is(':visible')) { this.date(this.date().clone().subtract(1, 'M')); } }, pageDown: function (widget) { if (widget.find('.datepicker').is(':visible')) { this.date(this.date().clone().add(1, 'M')); } }, enter: function () { this.hide(); }, escape: function () { this.hide(); }, 'control space': function (widget) { if (widget.find('.timepicker').is(':visible')) { widget.find('.btn[data-action=\"togglePeriod\"]').click(); } }, t: function () { this.date(moment()); }, 'delete': function () { this.clear(); } Allows for custom events to fire on keyboard press. keyBinds() Returns a string variable with the currently set options.keyBinds option. keyBinds(object) Takes an object value. Allows for several keyBinding functions to be specified for ease of access or accessibility. See the options page for defaults. debug 4.7.14 Default: false Will cause the date picker to stay open after a blur event. ignoreReadonly 4.7.14 Default: false Allow date picker show event to fire even when the associated input element has the readonly=\"readonly\" property. ignoreReadonly() Returns a boolean variable with the currently set options.ignoreReadonly option. ignoreReadonly(boolean) Takes a boolean value. Set this to true to allow the picker to be used even if the input field is readonly . This will not bypass the disabled property disabledTimeIntervals 4.14.30 Default: false Disables time selection between the given moments . disabledTimeIntervals() Returns an array variable with the currently set options.disabledTimeIntervals option. disabledTimeIntervals(array) Takes a array value. The array must be in the following format [moment(),moment()] For example: disabledTimeIntervals: [[moment({ h: 0 }), moment({ h: 8 })], [moment({ h: 18 }), moment({ h: 24 })]] Will disable times between 12-8am and 6-12pm today allowInputToggle 4.14.30 Default: false If true , the picker will show on textbox focus and icon click when used in a button group. allowInputToggle() Returns a boolean variable with the currently set options.allowInputToggle option. allowInputToggle(boolean) Takes a boolean value. If true , the picker will show on textbox focus and icon click when used in a button group focusOnShow 4.14.30 Default: true If false , the textbox will not be given focus when the picker is shown. focusOnShow() Returns a boolean variable with the currently set options.focusOnShow option. focusOnShow(boolean) Takes a boolean value. If false , the textbox will not be given focus when the picker is shown en/disabledHours 4.14.30 Issue: #851 Default: false disabledHours() Returns an array variable with the currently set options.en/disabledHours option. disabledHours(boolean) Takes a array value. Must be in 24 hour format. Will allow or disallow hour selections (much like disabledTimeIntervals ) but will affect all days. Like en/disabledDates , these options are mutually exclusive and will reset one of the options back to false. disabledHours: [0, 1, 2, 3, 4, 5, 6, 7, 8, 18, 19, 20, 21, 22, 23, 24] enabledHours: [9, 10, 11, 12, 13, 14, 15, 16] viewDate 4.14.30 Default: false This will change the viewDate without changing or setting the selected date. parseInputDate 4.14.30 Issue #1095 parseInputDate() Returns a function with the currently set options.parseInputDate parseInputDate(function) Takes a function Allows custom input formatting For example: the user can enter 'yesterday' or '30 days ago'. Example: var parseRelativeDate = function(relativeDate) { switch (relativeDate) { case 'today': return moment() case 'yesterday': return moment().subtract(1, 'day'); default: return moment() .subtract(Number(relativeDate.replace(\"days ago\", \"\").trim()), 'days'); } } var parseInputDate = function(inputDate) { var relativeDatePattern = /today|yesterday|[0-9]+\\s+(days ago)/, resultDate; if (moment.isMoment(inputDate) || inputDate instanceof Date) { resultDate = moment(inputDate); } else { var relativeDate = inputDate.match(relativeDatePattern), parseDate = null; if (relativeDate !== null) parseDate = this.parseRelativeDate(inputDate.match(relativeDatePattern)[0]); else parseDate = moment(); resultDate = moment(parseDate, \"YYYY-MM-DD\"); } return resultDate; } tooltips 4.15.35 tooltips: { today: 'Go to today', clear: 'Clear selection', close: 'Close the picker', selectMonth: 'Select Month', prevMonth: 'Previous Month', nextMonth: 'Next Month', selectYear: 'Select Year', prevYear: 'Previous Year', nextYear: 'Next Year', selectDecade: 'Select Decade', prevDecade: 'Previous Decade', nextDecade: 'Next Decade', prevCentury: 'Previous Century', nextCentury: 'Next Century' } This will change the tooltips over each icon to a custom string. tooltips() Returns an Ojbect of options.tooltips tooltips(tooltips) Takes an Ojbect of strings . Throws TypeError - if tooltips parameter is not an Ojbect timeZone 4.17.37 timeZone: '' Allows the setting of the Time Zone. You must include moment-timezone.js and moment-timzone data. See moment timezone documentation for usage. timeZone() Returns an string of options.timeZone timeZone(timeZone) Takes an string of a valid timezone. Throws TypeError - if tooltips parameter is not an string", "title": "Options"}, {"location": "Options/#options", "text": "Note All options are accessed via the data attribute e.g. $('#datetimepicker').data(\"DateTimePicker\").OPTION()", "title": "Options"}, {"location": "Options/#options_1", "text": "Returns the components current options object. Note that the changing the values of the returned object does not change the components actual configuration. Use options(options) to set the components options massively or the other methods for setting config options individually.", "title": "options()"}, {"location": "Options/#optionsoptions", "text": "Takes an object variable with option key:value properties and configures the component. Use this to update multiple options on the component.", "title": "options([options])"}, {"location": "Options/#date", "text": "Returns the component's model current date, a moment object or null if not set.", "title": "date"}, {"location": "Options/#datenewdate", "text": "Takes string, Date, moment, null parameter and sets the components model current moment to it. Passing a null value unsets the components model current moment. Parsing of the newDate parameter is made using moment library with the options.format and options.useStrict components configuration.", "title": "date([newDate])"}, {"location": "Options/#throws", "text": "TypeError - in case the newDate cannot be parsed", "title": "<PERSON><PERSON><PERSON>"}, {"location": "Options/#emits", "text": "dp.change - In case newDate is different from current moment", "title": "Emits"}, {"location": "Options/#format", "text": "Default: false See momentjs' docs for valid formats. Format also dictates what components are shown, e.g. MM/dd/YYYY will not display the time picker.", "title": "format"}, {"location": "Options/#format_1", "text": "Returns the component's options.format string", "title": "format()"}, {"location": "Options/#formatformat", "text": "Takes a moment.js format string and sets the components options.format . This is used for displaying and also for parsing input strings either from the input element the component is attached to or the date() function. The parameter can also be a boolean:false in which case the format is set to the locale's L LT . Note: this is also used to determine if the TimePicker sub component will display the hours in 12 or 24 format. (if 'a' or 'h' exists in the passed string then a 12 hour mode is set)", "title": "format(format)"}, {"location": "Options/#dayviewheaderformat", "text": "Default: 'MMMM YYYY' Changes the heading of the datepicker when in \"days\" view.", "title": "dayViewHeaderFormat"}, {"location": "Options/#dayviewheaderformat_1", "text": "Returns a string variable with the currently set options.dayViewHeaderFormat option.", "title": "dayViewHeaderFormat()"}, {"location": "Options/#dayviewheaderformatstring", "text": "Takes a string value. Used to customize the header of the day view.", "title": "dayViewHeaderFormat(string)"}, {"location": "Options/#extraformats", "text": "Default: false Allows for several input formats to be valid. See this PR .", "title": "extraFormats"}, {"location": "Options/#extraformats_1", "text": "Returns a boolean or array with the options.extraFormats option configuration", "title": "extraFormats()"}, {"location": "Options/#extraformatsformats", "text": "Takes an array of valid input moment format options.", "title": "extraFormats(formats)"}, {"location": "Options/#stepping", "text": "Default: 1 Number of minutes the up/down arrow's will move the minutes value in the time picker", "title": "stepping"}, {"location": "Options/#stepping_1", "text": "Returns a number with the options.stepping option configuration", "title": "stepping()"}, {"location": "Options/#steppingnumber", "text": "Takes a number . This be the amount the up/down arrows move the minute value with a time picker.", "title": "stepping(number)"}, {"location": "Options/#minmaxdate", "text": "Default: false Accepts: date, moment, string Prevents date/time selections before this date. Will override defaultDate and useCurrent if either of these settings are the same day since both options are invalid according to the rules you've selected.", "title": "min/maxDate"}, {"location": "Options/#mindate", "text": "Returns the currently set moment of the options.minDate or false if not set", "title": "minDate()"}, {"location": "Options/#mindatemindate", "text": "Takes a minDate string, Date, moment, boolean:false parameter and disallows the user to select a moment that is before that moment. If a boolean:false value is passed the options.minDate parameter is cleared and there is no restriction to the miminum moment the user can select. Note: If the minDate parameter is after the currently selected moment the currently selected moment changes to minDate parameter", "title": "minDate(minDate)"}, {"location": "Options/#throws_1", "text": "TypeError - if minDate parameter cannot be parsed using the options.format and options.useStrict configuration settings TypeError - if minDate parameter is after options.maxDate", "title": "<PERSON><PERSON><PERSON>"}, {"location": "Options/#emits_1", "text": "dp.change - if the new minDate is after currently selected moment (waiting for #472 to close in order to finalize this part) dp.error - if the new minDate is after currently selected moment (waiting for #472 to close in order to finalize this part)", "title": "Emits"}, {"location": "Options/#maxdate", "text": "Returns the currently set moment of the options.maxDate or false if not set", "title": "maxDate()"}, {"location": "Options/#maxdatemaxdate", "text": "Takes a maxDate string, Date, moment, boolean:false parameter and disallows the user to select a moment that is after that moment. If a boolean:false value is passed options.maxDate is cleared and there is no restriction to the maximum moment the user can select. Note: If maxDate is before the currently selected moment the currently selected moment changes to maxDate", "title": "maxDate(maxDate)"}, {"location": "Options/#throws_2", "text": "TypeError - if maxDate parameter cannot be parsed using the options.format and options.useStrict configuration settings TypeError - if maxDate parameter is before options.minDate", "title": "<PERSON><PERSON><PERSON>"}, {"location": "Options/#emits_2", "text": "dp.change - if the new maxDate is after currently selected moment (waiting for #472 to close in order to finalize this part) dp.error - if the new maxDate is after currently selected moment (waiting for #472 to close in order to finalize this part)", "title": "Emits"}, {"location": "Options/#usecurrent", "text": "Default: true On show , will set the picker to the current date/time.", "title": "useCurrent"}, {"location": "Options/#usecurrent_1", "text": "Returns a boolean or string with the options.useCurrent option configuration", "title": "useCurrent()"}, {"location": "Options/#usecurrentboolean-or-string", "text": "Takes a boolean or string . If a boolean true is passed and the components model moment is not set (either through setDate or through a valid value on the input element the component is attached to) then the first time the user opens the datetimepicker widget the value is initialized to the current moment of the action. If a false boolean is passed then no initialization happens on the input element. You can select the granularity on the initialized moment by passing one of the following strings ( 'year', 'month', 'day', 'hour', 'minute' ) in the variable. If for example you pass 'day' to the setUseCurrent function and the input field is empty the first time the user opens the datetimepicker widget the input text will be initialized to the current datetime with day granularity (ie if currentTime = 2014-08-10 13:32:33 the input value will be initialized to 2014-08-10 00:00:00 ) Note: If the options.defaultDate is set or the input element the component is attached to has already a value that takes precedence and the functionality of useCurrent is not triggered!", "title": "useCurrent(boolean or string)"}, {"location": "Options/#collapse", "text": "Default: true Using a Bootstraps collapse to switch between date/time pickers.", "title": "collapse"}, {"location": "Options/#collapse_1", "text": "Returns a boolean of the options.sideBySide .", "title": "collapse()"}, {"location": "Options/#collapsecollapse", "text": "Takes a boolean . If set to false the picker will display similar to sideBySide except vertical.", "title": "collapse(collapse)"}, {"location": "Options/#locale", "text": "Default: moment.locale() Accepts: string, moment.locale('locale') See momentjs for valid locales. You must include moment-with-locales.js or a local js file.", "title": "locale"}, {"location": "Options/#locale_1", "text": "Returns the currently set locale of the options.locale", "title": "locale()"}, {"location": "Options/#localenewlocale", "text": "Takes a string of any valid moment locale e.g. de for German.", "title": "locale(newLocale)"}, {"location": "Options/#throws_3", "text": "TypeError - if the locale is not loaded via a separate script or moment-with-locales", "title": "<PERSON><PERSON><PERSON>"}, {"location": "Options/#defaultdate", "text": "Default: false Accepts: date, moment, string Sets the picker default date/time. Overrides useCurrent", "title": "defaultDate"}, {"location": "Options/#defaultdate_1", "text": "Returns a moment with the options.defaultDate option configuration or false if not set", "title": "defaultDate()"}, {"location": "Options/#defaultdatedefaultdate", "text": "Takes a string, Date, moment, boolean:false . Will set the picker's inital date. If a boolean:false value is passed the options.defaultDate parameter is cleared. TypeError - if the provided date pass validation, including disabledDates , enabledDates , minDate , maxDate , and daysOfWeekDisabled TypeError - if the provided date cannot be parsed by momentjs", "title": "defaultDate(defaultDate)"}, {"location": "Options/#endisableddates", "text": "Default: false Accepts: array of [date, moment, string]", "title": "en/disabledDates"}, {"location": "Options/#disableddates", "text": "Returns an array with the currently set disabled dates on the component.", "title": "disabledDates()"}, {"location": "Options/#disableddatesdates", "text": "Takes an [ string or Date or moment ] of values and disallows the user to select those days. Setting this takes precedence over options.minDate , options.maxDate configuration. Also calling this function removes the configuration of options.enabledDates if such exist. Note: These values are matched with Day granularity.", "title": "disabledDates(dates)"}, {"location": "Options/#enableddates", "text": "Returns an array with the currently set enabled dates on the component.", "title": "enabledDates()"}, {"location": "Options/#enableddatesdates", "text": "Takes an [ string or Date or moment ] of values and allows the user to select only from those days. Setting this takes precedence over options.minDate , options.maxDate configuration. Also calling this function removes the configuration of options.disabledDates if such exist. Note: These values are matched with Day granularity.", "title": "enabledDates(dates)"}, {"location": "Options/#icons", "text": "Default: { time: 'glyphicon glyphicon-time', date: 'glyphicon glyphicon-calendar', up: 'glyphicon glyphicon-chevron-up', down: 'glyphicon glyphicon-chevron-down', previous: 'glyphicon glyphicon-chevron-left', next: 'glyphicon glyphicon-chevron-right', today: 'glyphicon glyphicon-screenshot', clear: 'glyphicon glyphicon-trash', close: 'glyphicon glyphicon-remove' } Accepts: object with all or some of the parameters above Change the default icons for the pickers functions.", "title": "icons"}, {"location": "Options/#icons_1", "text": "Returns an Ojbect of options.icons", "title": "icons()"}, {"location": "Options/#iconsicons", "text": "Takes an Ojbect of strings .", "title": "icons(icons)"}, {"location": "Options/#throws_4", "text": "TypeError - if icons parameter is not an Ojbect", "title": "<PERSON><PERSON><PERSON>"}, {"location": "Options/#usestrict", "text": "Default: false Defines if moment should use strict date parsing when considering a date to be valid.", "title": "useStrict"}, {"location": "Options/#usestrict_1", "text": "Returns a boolean of the options.useStrict", "title": "useStrict()"}, {"location": "Options/#usestrictusestrict", "text": "Takes a boolean . If useStrict is true , momentjs parsing rules will be stricter when determining if a date is valid or not.", "title": "useStrict(useStrict)"}, {"location": "Options/#sidebyside", "text": "Default: false Shows the picker side by side when using the time and date together.", "title": "sideBySide"}, {"location": "Options/#sidebyside_1", "text": "Returns a boolean of the options.sideBySide .", "title": "sideBySide()"}, {"location": "Options/#sidebysidesidebyside", "text": "Takes a boolean . If sideBySide is true and the time picker is used, both components will display side by side instead of collapsing.", "title": "sideBySide(sideBySide)"}, {"location": "Options/#daysofweekdisabled", "text": "Default: [] Accepts: array of numbers from 0-6 Disables the section of days of the week, e.g. weekends.", "title": "daysOfWeekDisabled"}, {"location": "Options/#daysofweekdisabled_1", "text": "Returns an array with the options.daysOfWeekDisabled configuration setting of the component.", "title": "daysOfWeekDisabled()"}, {"location": "Options/#daysofweekdisableddaysofweek", "text": "Takes an [ Number : 0 to 6 ] and disallow the user to select weekdays that exist in this array. This has lower priority over the options.minDate , options.maxDate , options.disabledDates and options.enabledDates configuration settings.", "title": "daysOfWeekDisabled(daysOfWeek)"}, {"location": "Options/#emits_3", "text": "dp.change - if the currently selected moment falls in the values passed on the daysOfWeek parameter. (waiting for #472 to close in order to finalize this part) dp.error - if the currently selected moment falls in the values passed on the daysOfWeek parameter. (waiting for #472 to close in order to finalize this part)", "title": "Emits"}, {"location": "Options/#calendarweeks", "text": "Default: false Shows the week of the year to the left of first day of the week.", "title": "calendarWeeks"}, {"location": "Options/#calendarweeks_1", "text": "Returns a boolean with the current options.calendarWeeks option configuration", "title": "calendarWeeks()"}, {"location": "Options/#calendarweeksboolean", "text": "Takes a boolean variable to set if the week numbers will appear to the left on the days view", "title": "calendarWeeks(boolean)"}, {"location": "Options/#viewmode", "text": "Default: 'days' Accepts: 'decades','years','months','days' The default view to display when the picker is shown. Note : To limit the picker to selecting, for instance the year and month, use format: MM/YYYY .", "title": "viewMode"}, {"location": "Options/#viewmode_1", "text": "Returns a string of the options.viewMode .", "title": "viewMode()"}, {"location": "Options/#viewmodenewviewmode", "text": "Takes a string . Valid values are 'days' , 'months' , 'years' and 'decades'", "title": "viewMode(newViewMode)"}, {"location": "Options/#throws_5", "text": "TypeError - if newViewMode parameter is not an a string or if newViewMode is not a valid value.", "title": "<PERSON><PERSON><PERSON>"}, {"location": "Options/#toolbarplacement", "text": "Default: 'default' Accepts: 'default', 'top', 'bottom' Changes the placement of the icon toolbar.", "title": "toolbarPlacement"}, {"location": "Options/#toolbarplacement_1", "text": "Returns a string variable with the currently set options.toolbarplacement option.", "title": "toolbarplacement()"}, {"location": "Options/#toolbarplacementstring", "text": "Takes a string value. Valid values are 'default' , 'top' and 'bottom' . Changes the placement of the toolbar where the today, clear, component switch icon are located.", "title": "toolbarplacement(string)"}, {"location": "Options/#showtodaybutton", "text": "Default: false Show the \"Today\" button in the icon toolbar. Clicking the \"Today\" button will set the calendar view and set the date to now .", "title": "showTodayButton"}, {"location": "Options/#showtodaybutton_1", "text": "Returns a boolean variable with the currently set options.showTodayButton option.", "title": "showTodayButton()"}, {"location": "Options/#showtodaybuttonboolean", "text": "Takes a boolean variable to set if the Today button will appear on the widget", "title": "show<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(boolean)"}, {"location": "Options/#showclear", "text": "Default: false Show the \"Clear\" button in the icon toolbar. Clicking the \"Clear\" button will set the calendar to null.", "title": "showClear"}, {"location": "Options/#showclear_1", "text": "Returns a boolean variable with the currently set options.showClear option.", "title": "showClear()"}, {"location": "Options/#showclearboolean", "text": "Takes a boolean variable to set if the clear date button will appear on the widget", "title": "showClear(boolean)"}, {"location": "Options/#showclose", "text": "Default: false Show the \"Close\" button in the icon toolbar. Clicking the \"Close\" button will call hide()", "title": "showClose"}, {"location": "Options/#showclose_1", "text": "Returns a boolean variable with the currently set options.showClose option.", "title": "showClose()"}, {"location": "Options/#showcloseboolean", "text": "Takes a boolean value. If true , an icon will be displayed on the toolbar that will hide the picker", "title": "showClose(boolean)"}, {"location": "Options/#widgetpositioning", "text": "Default: { horizontal: 'auto' vertical: 'auto' } Accepts: object with the all or one of the parameters above horizontal: 'auto', 'left', 'right' vertical: 'auto', 'top', 'bottom'", "title": "widgetPositioning"}, {"location": "Options/#widgetpositioning_1", "text": "Returns the currently set options.widgetPositioning object containing two keys horizontal and vertical", "title": "widgetPositioning()"}, {"location": "Options/#widgetpositioningpositioningobject", "text": "Takes an object parameter that can contain two keys vertical and horizontal each having a value of 'auto', 'top', 'bottom' for vertical and 'auto', 'left', 'right' for horizontal which defines where the dropdown with the widget will appear relative to the input element the component is attached to. 'auto' is the default value for both horizontal and vertical keys and it tries to automatically place the dropdown in a position that is visible to the user. Usually you should not override those options unless you have a special need in your layout.", "title": "widgetPositioning(positioningObject)"}, {"location": "Options/#widgetparent", "text": "Default: null Accepts: string or jQuery object On picker show, places the widget at the identifier (string) or jQuery object if the element has css position: 'relative'", "title": "widgetParent"}, {"location": "Options/#widgetparent_1", "text": "Returns a $(element) variable with the currently set options.widgetParent option.", "title": "widgetParent()"}, {"location": "Options/#widgetparentwidgetparent", "text": "Takes a string or $(element) value.", "title": "widgetParent(widgetParent)"}, {"location": "Options/#keepopen", "text": "Default: false Will cause the date picker to stay open after selecting a date.", "title": "keep<PERSON>pen"}, {"location": "Options/#keepopen_1", "text": "Returns a boolean variable with the currently set options.keepOpen option.", "title": "keepOpen()"}, {"location": "Options/#keepopenboolean", "text": "Takes a boolean value.", "title": "keep<PERSON><PERSON>(boolean)"}, {"location": "Options/#inline", "text": "Default: false Will display the picker inline without the need of a input field. This will also hide borders and shadows.", "title": "inline"}, {"location": "Options/#inline_1", "text": "Returns a boolean variable with the currently set options.inline option.", "title": "inline()"}, {"location": "Options/#inlineboolean", "text": "Takes a boolean value.", "title": "inline(boolean)"}, {"location": "Options/#keepinvalid", "text": "4.7.14 De<PERSON><PERSON>: false Will cause the date picker to not revert or overwrite invalid dates.", "title": "keepInvalid"}, {"location": "Options/#keepinvalid_1", "text": "Returns a string variable with the currently set options.keepInvalid option.", "title": "keepInvalid()"}, {"location": "Options/#keepinvalidboolean", "text": "Takes a boolean value. If true , invalid dates will not be reverted to a previous selection or changed.", "title": "keepInvalid(boolean)"}, {"location": "Options/#keybinds", "text": "Default: up: function (widget) { if (widget.find('.datepicker').is(':visible')) { this.date(this.date().clone().subtract(7, 'd')); } else { this.date(this.date().clone().add(1, 'm')); } }, down: function (widget) { if (!widget) { this.show(); } else if (widget.find('.datepicker').is(':visible')) { this.date(this.date().clone().add(7, 'd')); } else { this.date(this.date().clone().subtract(1, 'm')); } }, 'control up': function (widget) { if (widget.find('.datepicker').is(':visible')) { this.date(this.date().clone().subtract(1, 'y')); } else { this.date(this.date().clone().add(1, 'h')); } }, 'control down': function (widget) { if (widget.find('.datepicker').is(':visible')) { this.date(this.date().clone().add(1, 'y')); } else { this.date(this.date().clone().subtract(1, 'h')); } }, left: function (widget) { if (widget.find('.datepicker').is(':visible')) { this.date(this.date().clone().subtract(1, 'd')); } }, right: function (widget) { if (widget.find('.datepicker').is(':visible')) { this.date(this.date().clone().add(1, 'd')); } }, pageUp: function (widget) { if (widget.find('.datepicker').is(':visible')) { this.date(this.date().clone().subtract(1, 'M')); } }, pageDown: function (widget) { if (widget.find('.datepicker').is(':visible')) { this.date(this.date().clone().add(1, 'M')); } }, enter: function () { this.hide(); }, escape: function () { this.hide(); }, 'control space': function (widget) { if (widget.find('.timepicker').is(':visible')) { widget.find('.btn[data-action=\"togglePeriod\"]').click(); } }, t: function () { this.date(moment()); }, 'delete': function () { this.clear(); } Allows for custom events to fire on keyboard press.", "title": "keyBinds"}, {"location": "Options/#keybinds_1", "text": "Returns a string variable with the currently set options.keyBinds option.", "title": "keyBinds()"}, {"location": "Options/#keybindsobject", "text": "Takes an object value. Allows for several keyBinding functions to be specified for ease of access or accessibility. See the options page for defaults.", "title": "keyBinds(object)"}, {"location": "Options/#debug", "text": "4.7.14 De<PERSON><PERSON>: false Will cause the date picker to stay open after a blur event.", "title": "debug"}, {"location": "Options/#ignorereadonly", "text": "4.7.14 De<PERSON>ult: false Allow date picker show event to fire even when the associated input element has the readonly=\"readonly\" property.", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"location": "Options/#ignorereadonly_1", "text": "Returns a boolean variable with the currently set options.ignoreReadonly option.", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>()"}, {"location": "Options/#ignorereadonlyboolean", "text": "Takes a boolean value. Set this to true to allow the picker to be used even if the input field is readonly . This will not bypass the disabled property", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>(boolean)"}, {"location": "Options/#disabledtimeintervals", "text": "4.14.30 <PERSON><PERSON><PERSON>: false Disables time selection between the given moments .", "title": "disabledTimeIntervals"}, {"location": "Options/#disabledtimeintervals_1", "text": "Returns an array variable with the currently set options.disabledTimeIntervals option.", "title": "disabledTimeIntervals()"}, {"location": "Options/#disabledtimeintervalsarray", "text": "Takes a array value. The array must be in the following format [moment(),moment()] For example: disabledTimeIntervals: [[moment({ h: 0 }), moment({ h: 8 })], [moment({ h: 18 }), moment({ h: 24 })]] Will disable times between 12-8am and 6-12pm today", "title": "disabledTimeIntervals(array)"}, {"location": "Options/#allowinputtoggle", "text": "4.14.30 Default: false If true , the picker will show on textbox focus and icon click when used in a button group.", "title": "allowInputToggle"}, {"location": "Options/#allowinputtoggle_1", "text": "Returns a boolean variable with the currently set options.allowInputToggle option.", "title": "allowInputToggle()"}, {"location": "Options/#allowinputtoggleboolean", "text": "Takes a boolean value. If true , the picker will show on textbox focus and icon click when used in a button group", "title": "allowInputToggle(boolean)"}, {"location": "Options/#focusonshow", "text": "4.14.30 De<PERSON>ult: true If false , the textbox will not be given focus when the picker is shown.", "title": "focusOnShow"}, {"location": "Options/#focusonshow_1", "text": "Returns a boolean variable with the currently set options.focusOnShow option.", "title": "focusOnShow()"}, {"location": "Options/#focusonshowboolean", "text": "Takes a boolean value. If false , the textbox will not be given focus when the picker is shown", "title": "focusOnShow(boolean)"}, {"location": "Options/#endisabledhours", "text": "4.14.30 Issue: #851 De<PERSON>ult: false", "title": "en/disabledHours"}, {"location": "Options/#disabledhours", "text": "Returns an array variable with the currently set options.en/disabledHours option.", "title": "disabledHours()"}, {"location": "Options/#disabledhoursboolean", "text": "Takes a array value. Must be in 24 hour format. Will allow or disallow hour selections (much like disabledTimeIntervals ) but will affect all days. Like en/disabledDates , these options are mutually exclusive and will reset one of the options back to false. disabledHours: [0, 1, 2, 3, 4, 5, 6, 7, 8, 18, 19, 20, 21, 22, 23, 24] enabledHours: [9, 10, 11, 12, 13, 14, 15, 16]", "title": "disabledHours(boolean)"}, {"location": "Options/#viewdate", "text": "4.14.30 De<PERSON><PERSON>: false This will change the viewDate without changing or setting the selected date.", "title": "viewDate"}, {"location": "Options/#parseinputdate", "text": "4.14.30 Issue #1095", "title": "parseInputDate"}, {"location": "Options/#parseinputdate_1", "text": "Returns a function with the currently set options.parseInputDate", "title": "parseInputDate()"}, {"location": "Options/#parseinputdatefunction", "text": "Takes a function Allows custom input formatting For example: the user can enter 'yesterday' or '30 days ago'. Example: var parseRelativeDate = function(relativeDate) { switch (relativeDate) { case 'today': return moment() case 'yesterday': return moment().subtract(1, 'day'); default: return moment() .subtract(Number(relativeDate.replace(\"days ago\", \"\").trim()), 'days'); } } var parseInputDate = function(inputDate) { var relativeDatePattern = /today|yesterday|[0-9]+\\s+(days ago)/, resultDate; if (moment.isMoment(inputDate) || inputDate instanceof Date) { resultDate = moment(inputDate); } else { var relativeDate = inputDate.match(relativeDatePattern), parseDate = null; if (relativeDate !== null) parseDate = this.parseRelativeDate(inputDate.match(relativeDatePattern)[0]); else parseDate = moment(); resultDate = moment(parseDate, \"YYYY-MM-DD\"); } return resultDate; }", "title": "parseInputDate(function)"}, {"location": "Options/#tooltips", "text": "4.15.35 tooltips: { today: 'Go to today', clear: 'Clear selection', close: 'Close the picker', selectMonth: 'Select Month', prevMonth: 'Previous Month', nextMonth: 'Next Month', selectYear: 'Select Year', prevYear: 'Previous Year', nextYear: 'Next Year', selectDecade: 'Select Decade', prevDecade: 'Previous Decade', nextDecade: 'Next Decade', prevCentury: 'Previous Century', nextCentury: 'Next Century' } This will change the tooltips over each icon to a custom string.", "title": "tooltips"}, {"location": "Options/#tooltips_1", "text": "Returns an Ojbect of options.tooltips", "title": "tooltips()"}, {"location": "Options/#tooltipstooltips", "text": "Takes an Ojbect of strings .", "title": "tooltips(tooltips)"}, {"location": "Options/#throws_6", "text": "TypeError - if tooltips parameter is not an Ojbect", "title": "<PERSON><PERSON><PERSON>"}, {"location": "Options/#timezone", "text": "4.17.37 timeZone: '' Allows the setting of the Time Zone. You must include moment-timezone.js and moment-timzone data. See moment timezone documentation for usage.", "title": "timeZone"}, {"location": "Options/#timezone_1", "text": "Returns an string of options.timeZone", "title": "timeZone()"}, {"location": "Options/#timezonetimezone", "text": "Takes an string of a valid timezone.", "title": "timeZone(timeZone)"}, {"location": "Options/#throws_7", "text": "TypeError - if tooltips parameter is not an string", "title": "<PERSON><PERSON><PERSON>"}, {"location": "Version%204%20Changelog/", "text": "The page has moved to: this page", "title": "Version 4 Changelog"}, {"location": "Version%204%20Contributors%20guide/", "text": "The page has moved to: this page", "title": "Version 4 Contributors guide"}]}