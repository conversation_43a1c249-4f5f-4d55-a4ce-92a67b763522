<?php

namespace app\admin\model\system;

use think\Model;
use traits\model\SoftDelete;
use think\Db;

class Customer extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'customer';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [

    ];
    

    /**
     * 同步客户信息到子产品数据库
     * @param string $customer_name 客户名称
     * @param string $old_customer_name 旧客户名称
     */
    public function sync($customer_name, $old_customer_name = ''){
        $platform = \app\admin\model\system\Platform::select();

        $time = time();
        foreach ($platform as $key => $value) {
            switch ($value->config_name) {
                case 'aimaster':
                    $query = Db::connect('aimaster')->name('ai_unit');
                    if($old_customer_name){
                        $row = $query->where('name', $old_customer_name)->find();
                        if($row){
                            $query->where('id', $row['id'])->update(['name'=>$customer_name,'updatetime'=>$time]);
                        }else{
                            $query->insert(['name'=>$customer_name,'createtime'=>$time,'updatetime'=>$time]);
                        }
                    }else{
                        $query->insert(['name'=>$customer_name,'createtime'=>$time,'updatetime'=>$time]);
                    }
                    break;
                case 'vtrs':
                    $query = Db::connect('vtrs')->name('user_school');
                    if($old_customer_name){
                        $row = $query->where('name', $old_customer_name)->find();
                        if($row){
                            $query->where('id', $row['id'])->update(['name'=>$customer_name,'updatetime'=>$time]);
                        }else{
                            $query->insert(['name'=>$customer_name,'createtime'=>$time,'updatetime'=>$time]);
                        }
                    }else{
                        $query->insert(['name'=>$customer_name,'createtime'=>$time,'updatetime'=>$time]);
                    }
                    break;
                case 'formmaster':
                    $query = Db::connect('formmaster')->name('school');
                    if($old_customer_name){
                        $row = $query->where('name', $old_customer_name)->find();
                        if($row){
                            $query->where('id', $row['id'])->update(['name'=>$customer_name,'updatetime'=>$time]);
                        }else{
                            $query->insert(['name'=>$customer_name,'createtime'=>$time,'updatetime'=>$time]);
                        }
                    }else{
                        $query->insert(['name'=>$customer_name,'createtime'=>$time,'updatetime'=>$time]);
                    }
                    break;
                case 'examadmin':
                    $query = Db::connect('examadmin')->name('school');
                    if($old_customer_name){
                        $row = $query->where('name', $old_customer_name)->find();
                        if($row){
                            $query->where('id', $row['id'])->update(['name'=>$customer_name,'updatetime'=>$time]);
                        }else{
                            $query->insert(['name'=>$customer_name,'createtime'=>$time,'updatetime'=>$time]);
                        }
                    }else{
                        $query->insert(['name'=>$customer_name,'createtime'=>$time,'updatetime'=>$time]);
                    }
                break;
            }
        }
    }






}
