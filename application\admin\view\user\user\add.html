<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    {:token()}
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Group')}:</label>
        <div class="col-xs-12 col-sm-4">
            {$groupList}
        </div>
    </div>

    <!-- <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('所属客户')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-customer_id" min="0" data-rule="required" data-source="system/customer/index" class="form-control selectpage" name="row[customer_id]" type="text" value="" data-field="customer_name" data-primary-key="customer_id">
        </div>
    </div>

    <div class="form-group" id="c-org-content" style="display: none;">
        <label class="control-label col-xs-12 col-sm-2">{:__('所属组织')}:</label>
        <div class="col-xs-12 col-sm-10">
            <div class="col-xs-12 col-sm-3" id="c-org_id-content" style="display: none;margin: 0 10px;">
                <div class="form-group">
                    <label class="control-label">{:__('一级组织')}</label>
                    <input id="c-org_id" min="0" data-rule="required" data-source="system/org/index" class="form-control selectpage" name="row[org_id]" type="text" value="" data-field="org_name" data-primary-key="org_id">
                </div>
            </div>
            <div class="col-xs-12 col-sm-3" id="c-org_id1-content" style="display: none;margin: 0 10px;">
                <div class="form-group">
                    <label class="control-label">{:__('二级组织')}</label>
                    <input id="c-org_id1" min="0" data-source="system/org/index" class="form-control selectpage" name="row[org_id1]" type="text" value="" data-field="org_name" data-primary-key="org_id">
                </div>
            </div>
            <div class="col-xs-12 col-sm-3" id="c-org_id2-content" style="display: none;margin: 0 10px;">
                <div class="form-group">
                    <label class="control-label">{:__('三级组织')}</label>
                    <input id="c-org_id2" min="0" data-source="system/org/index" class="form-control selectpage" name="row[org_id2]" type="text" value="" data-field="org_name" data-primary-key="org_id">
                </div>
            </div>
            <div class="col-xs-12 col-sm-3" id="c-org_id3-content" style="display: none;margin: 0 10px;">
                <div class="form-group">
                    <label class="control-label">{:__('四级组织')}</label>
                    <input id="c-org_id3" min="0" data-source="system/org/index" class="form-control selectpage" name="row[org_id3]" type="text" value="" data-field="org_name" data-primary-key="org_id">
                </div>
            </div>
        </div>
    </div> -->

    <div class="form-group">
        <label for="c-username" class="control-label col-xs-12 col-sm-2">{:__('Username')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-username" data-rule="required" class="form-control" name="row[username]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label for="c-nickname" class="control-label col-xs-12 col-sm-2">{:__('Nickname')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-nickname" data-rule="required" class="form-control" name="row[nickname]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label for="c-password" class="control-label col-xs-12 col-sm-2">{:__('Password')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-password" data-rule="password" class="form-control" name="row[password]" type="password" value="" placeholder="{:__('Leave password blank if dont want to change')}" autocomplete="new-password" />
        </div>
    </div>
    <div class="form-group">
        <label for="c-mobile" class="control-label col-xs-12 col-sm-2">{:__('Mobile')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-mobile" data-rule="mobile" class="form-control" name="row[mobile]" type="text" >
        </div>
    </div>
    <div class="form-group">
        <label for="c-avatar" class="control-label col-xs-12 col-sm-2">{:__('Avatar')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-avatar" data-rule="" class="form-control" size="50" name="row[avatar]" type="text">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-avatar" class="btn btn-danger faupload" data-input-id="c-avatar" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-avatar"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-avatar" class="btn btn-primary fachoose" data-input-id="c-avatar" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-avatar"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-avatar"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[status]', ['normal'=>__('Normal'), 'hidden'=>__('Hidden')], 'normal')}
        </div>
    </div>
    <input type="hidden" id="c-customer_id" value="" name="row[customer_id]">
    <input type="hidden" id="c-org_id" value="" name="row[org_id]">
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
