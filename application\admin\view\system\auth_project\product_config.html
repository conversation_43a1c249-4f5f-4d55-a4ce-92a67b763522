<style>
    .product-config-form .form-group {
        margin-bottom: 15px;
    }
    .product-config-form .control-label {
        font-weight: bold;
        margin-bottom: 5px;
    }
    .sub-product-item {
        border: 1px solid #ddd;
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 5px;
        background-color: #f9f9f9;
    }
    .sub-product-item .form-group {
        margin-bottom: 10px;
    }
</style>

<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        产品配置
    </div>
    <div class="panel-body">
        <form id="product-config-form" class="product-config-form" role="form" method="POST" action="">
            <input type="hidden" name="row[auth_id]" value="{$auth_id ?? ''}">
            <!-- 主产品配置 -->
            <div class="fieldset-section">
                <h4>主产品配置</h4>
                <hr>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label">大标题</label>
                            <input type="text" name="row[first_title]" data-rule="required" class="form-control" value="{$row.first_title ?? ''}" />
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label">副标题</label>
                            <input type="text" name="row[second_title]" data-rule="required" class="form-control" value="{$row.second_title ?? ''}" />
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label">logo</label>
                            <div class="input-group">
                                <input id="c-logo" data-rule="" data-rule="required" class="form-control" size="50" name="row[logo]" type="text" value="{$row.logo ?? ''}">
                                <div class="input-group-addon no-border no-padding">
                                    <span><button type="button" id="faupload-logo" class="btn btn-danger faupload" data-input-id="c-logo" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-logo"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                                    <span><button type="button" id="fachoose-logo" class="btn btn-primary fachoose" data-input-id="c-logo" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                                </div>
                                <span class="msg-box n-right" for="c-logo"></span>
                            </div>
                            <ul class="row list-inline faupload-preview" id="p-logo"></ul>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label">学校名称</label>
                            <input type="text" name="row[school_name]" data-rule="required" class="form-control" value="{$row.school_name ?? ''}" />
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label">课程名称</label>
                            <input type="text" name="row[course_title]" data-rule="required" class="form-control" value="{$row.course_title ?? ''}" />
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label">标签</label>
                            <input type="text" name="row[course_tag]" data-rule="required"  data-role="tagsinput" class="form-control" value="{$row.course_tag ?? ''}" />
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label">课程封面</label>
                            <div class="input-group">
                                <input id="c-course_image" data-rule="required" class="form-control" size="50" name="row[course_image]" type="text" value="{$row.course_image ?? ''}">
                                <div class="input-group-addon no-border no-padding">
                                    <span><button type="button" id="faupload-course_image" class="btn btn-danger faupload" data-input-id="c-course_image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-course_image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                                    <span><button type="button" id="fachoose-course_image" class="btn btn-primary fachoose" data-input-id="c-course_image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                                </div>
                                <span class="msg-box n-right" for="c-course_image"></span>
                            </div>
                            <ul class="row list-inline faupload-preview" id="p-course_image"></ul>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label">课程课时</label>
                            <input id="c-course_hours" class="form-control" data-rule="required" name="row[course_hours]" type="number" value="{$row.course_hours ?? ''}">
                        </div>
                    </div>
                </div>
                
                
                 
                
                <div class="form-group">
                    <label class="control-label">课程描述</label>
                    <textarea name="row[course_description]" data-rule="required" class="form-control" rows="3">{$row.course_description ?? ''}</textarea>
                </div>
                

                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="control-label">模块一名称</label>
                            <input type="text" name="row[module_one_name]" data-rule="required" class="form-control" value="{$row.module_one_name ?? ''}" />
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="control-label">模块二名称</label>
                            <input type="text" name="row[module_two_name]" data-rule="required" class="form-control" value="{$row.module_two_name ?? ''}" />
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="control-label">模块三名称</label>
                            <input type="text" name="row[module_three_name]" data-rule="required" class="form-control" value="{$row.module_three_name ?? ''}" />
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="control-label">模块一英文</label>
                            <input type="text" name="row[module_one_en_name]" class="form-control" value="{$row.module_one_en_name ?? ''}" />
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="control-label">模块二英文</label>
                            <input type="text" name="row[module_two_en_name]" class="form-control" value="{$row.module_two_en_name ?? ''}" />
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="control-label">模块三英文</label>
                            <input type="text" name="row[module_three_en_name]" class="form-control" value="{$row.module_three_en_name ?? ''}" />
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 子产品配置 -->
            <div class="fieldset-section">
                <h4>子产品配置</h4>
                <hr>
                <div id="sub-products-container">
                    {foreach name="sub_products" id="item" key="key"}
                    <div class="sub-product-item" data-index="{$key}">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="control-label">子产品LOGO</label>
                                    <div class="input-group">
                                        <input id="c-sub-image-{$key}" data-rule="required" class="form-control" size="50" name="sub_products[{$key}][image]" type="text" value="{$item.image ?? ''}">
                                        <div class="input-group-addon no-border no-padding">
                                            <span><button type="button" id="faupload-sub-image-{$key}" class="btn btn-danger faupload" data-input-id="c-sub-image-{$key}" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-sub-image-{$key}"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                                            <span><button type="button" id="fachoose-sub-image-{$key}" class="btn btn-primary fachoose" data-input-id="c-sub-image-{$key}" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                                        </div>
                                        <span class="msg-box n-right" for="c-sub-image-{$key}"></span>
                                    </div>
                                    <ul class="row list-inline faupload-preview" id="p-sub-image-{$key}"></ul>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="control-label">名称</label>
                                    <input type="text" name="sub_products[{$key}][name]" data-rule="required" class="form-control" value="{$item.name ?? ''}" />
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="control-label">产品</label>
                                    <input type="text" name="sub_products[{$key}][product_name]" class="form-control" value="{$item.product_name ?? ''}" readonly />
                                    <input type="hidden" name="sub_products[{$key}][product_id]" value="{$item.product_id ?? ''}" />
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="control-label">所属模块</label>
                                        <select name="sub_products[{$key}][module]" data-rule="required" class="form-control">
                                            <option value="">请选择模块</option>
                                            <option value="1" {if ($item.module ?? '') == 1}selected{/if}>模块一</option>
                                            <option value="2" {if ($item.module ?? '') == 2}selected{/if}>模块二</option>
                                            <option value="3" {if ($item.module ?? '') == 3}selected{/if}>模块三</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="control-label">权重</label>
                                        <input type="text" data-rule="required" name="sub_products[{$key}][weigh]" class="form-control" value="{$item.weigh ?? ''}" />
                                    </div>
                                </div>
                            </div>
                            <!-- <div class="col-md-1">
                                <div class="form-group" style="margin-top: 25px;">
                                    <button type="button" class="btn btn-danger btn-remove-sub">删除</button>
                                </div>
                            </div> -->
                        </div>
                        <div class="form-group">
                            <label class="control-label">产品描述</label>
                            <textarea name="sub_products[{$key}][description]" class="form-control" rows="2">{$item.description ?? ''}</textarea>
                        </div>
                    </div>
                    {/foreach}
                </div>
                
                <!-- <div class="form-group">
                    <button type="button" class="btn btn-success" id="btn-add-sub-product">
                        <i class="fa fa-plus"></i> 添加子产品
                    </button>
                </div> -->
            </div>

            <div class="form-group layer-footer">
                <label class="control-label col-xs-12 col-sm-1"></label>
                <div class="col-xs-12 col-sm-8">
                    <button type="submit" class="btn btn-primary disabled">保存配置</button>
                <button type="reset" class="btn btn-default">重置</button>
                </div>
            </div>
            
        </form>
    </div>
</div>

<!-- <script>
    require(['jquery', 'bootstrap', 'backend'], function ($, undefined, Backend) {
        $(document).ready(function() {
            // 添加子产品
            $('#btn-add-sub-product').on('click', function() {
                var index = $('#sub-products-container .sub-product-item').length;
                var subProductHtml = `
                    <div class="sub-product-item" data-index="${index}">
                        <div class="row">
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label class="control-label">子产品LOGO</label>
                                    <input type="hidden" name="sub_products[${index}][logo]" class="sub-logo" value="" />
                                    <div class="input-group">
                                        <input type="text" class="form-control" readonly>
                                        <div class="input-group-addon no-border no-padding">
                                            <span><button type="button" class="btn btn-danger btn-xs btn-upload-sub" data-input-id="sub-logo-${index}"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                                            <span><button type="button" class="btn btn-default btn-xs btn-select-img" data-input-id="sub-logo-${index}"><i class="fa fa-list"></i> {:__('Select')}</button></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="control-label">名称</label>
                                    <input type="text" name="sub_products[${index}][name]" class="form-control" value="" />
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="control-label">产品</label>
                                    <input type="text" name="sub_products[${index}][product]" class="form-control" value="" readonly />
                                    <input type="hidden" name="sub_products[${index}][product_id]" value="" />
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="control-label">所属平台</label>
                                    <select name="sub_products[${index}][platform]" class="form-control">
                                        <option value="">请选择平台</option>
                                        <option value="web">Web</option>
                                        <option value="app">App</option>
                                        <option value="mini_program">小程序</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-1">
                                <div class="form-group" style="margin-top: 25px;">
                                    <button type="button" class="btn btn-danger btn-remove-sub">删除</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="control-label">产品描述</label>
                            <textarea name="sub_products[${index}][description]" class="form-control" rows="2"></textarea>
                        </div>
                    </div>
                `;
                $('#sub-products-container').append(subProductHtml);
            });
            
            // 删除子产品
            $(document).on('click', '.btn-remove-sub', function() {
                $(this).closest('.sub-product-item').remove();
            });
            
            // 表单提交
            $('#product-config-form').on('submit', function(e) {
                e.preventDefault();
                var formData = $(this).serialize();
                
                $.ajax({
                    url: window.location.href,
                    type: 'POST',
                    data: formData,
                    success: function(res) {
                        if (res.code === 1) {
                            Toastr.success(res.msg);
                        } else {
                            Toastr.error(res.msg);
                        }
                    },
                    error: function() {
                        Toastr.error('保存失败');
                    }
                });
            });
        });
    });
</script> -->