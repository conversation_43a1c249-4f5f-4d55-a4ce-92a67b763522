<script>
    var valid_result = false
    {notempty name="row"}
    var row = {:json_encode($row, 256)};
    {else/}
    var row = {};
    {/notempty}

    var app = new Vue({
        el: '#app',
        data: {
            outbounds: []
        },
        created() {
            // 编辑时初始化数据
            if (row) {
                let outbounds = row.outbounds ? row.outbounds : []
                this.outbounds = outbounds
                console.log('outbounds', this.outbounds)
            }
        },
        mounted() {
            // 绑定提交按钮事件
            $('#submit-btn').on('click', function() {
                // 触发验证按钮
                $('#valid').click();
                
                // 检查验证结果
                if (valid_result) {
                    // 验证通过，提交表单
                    $('#add-form, #edit-form').submit();
                }
            });
        },
        methods: {
            // 打开出库选择
            openOutboundSelect() {
                console.log('openOutboundSelect')

                Fast.api.open('system/outbound/select', '选择出库单', {
                    area: ['90%', '90%'],
                    callback: (data) => {
                        console.log('outbound select callback', data)


                        // 排除已经存在的产品
                        for (let i = 0; i < this.outbounds.length; i++) {
                            for (let j = 0; j < data.length; j++) {
                                if (this.outbounds[i].product_id == data[j].product_id) {
                                    data.splice(j, 1)
                                }
                            }
                        }
                        this.outbounds = this.outbounds.concat(data)
                    }
                })
            },
            // 删除产品
            deleteOutbound(index) {
                console.log('deleteOutbound', index)
                this.outbounds.splice(index, 1)
            },
            // 验证
            valid() {
                console.log('valid')
                // 将产品信息转换为JSON字符串
                let outbound = []
                if(this.outbounds.length <1){
                    Fast.api.msg('请选择出库单')
                    valid_result = false
                    return false
                }
                for (let i = 0; i < this.outbounds.length; i++) {
                    let outbounds = this.outbounds[i]
                    outbound.push({
                        id: outbounds.outbound_id,
                    })
                }
                valid_result = true
                $('#c-outbounds').val(JSON.stringify(outbound))
            }
        }
    })
</script>
<style>
    .w-60 {
        width: 60px;
    }

    .m-l-25 {
        margin-left: 25px !important;
    }

    .p-l-80 {
        margin-left: 80px !important;
    }

    .el-input-number--mini {
        width: 90px;
        line-height: 26px;
    }
</style>