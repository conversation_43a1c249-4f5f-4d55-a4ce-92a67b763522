<?php

namespace app\admin\model\system;

use think\Model;
use traits\model\SoftDelete;

class Org extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'org';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [

    ];
    

    







    public function customer()
    {
        return $this->belongsTo('app\admin\model\Customer', 'customer_id', 'customer_id', [], 'LEFT')->setEagerlyType(0);
    }
}
