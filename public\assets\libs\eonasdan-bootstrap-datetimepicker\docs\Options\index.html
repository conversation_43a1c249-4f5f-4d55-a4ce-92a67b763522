﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    
    
    <link rel="apple-touch-icon" sizes="57x57" href="/apple-touch-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="114x114" href="/apple-touch-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="72x72" href="/apple-touch-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="144x144" href="/apple-touch-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="60x60" href="/apple-touch-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="120x120" href="/apple-touch-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="76x76" href="/apple-touch-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="152x152" href="/apple-touch-icon-152x152.png">
    <link rel="icon" type="image/png" href="/favicon-196x196.png" sizes="196x196">
    <link rel="icon" type="image/png" href="/favicon-160x160.png" sizes="160x160">
    <link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96">
    <link rel="icon" type="image/png" href="/favicon-16x16.png" sizes="16x16">
    <link rel="icon" type="image/png" href="/favicon-32x32.png" sizes="32x32">
    <meta name="msapplication-TileColor" content="#2b5797">
    <meta name="msapplication-TileImage" content="/mstile-144x144.png">

    <title></title>

    <link rel="stylesheet" type="text/css" media="screen"
          href="//maxcdn.bootstrapcdn.com/bootstrap/3.3.1/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="//maxcdn.bootstrapcdn.com/font-awesome/4.3.0/css/font-awesome.min.css">
    <link href="../css/prettify-1.0.css" rel="stylesheet">
    <link href="../css/base.css" rel="stylesheet">
    <link href="//cdn.rawgit.com/Eonasdan/bootstrap-datetimepicker/e8bddc60e73c1ec2475f827be36e1957af72e2ea/build/css/bootstrap-datetimepicker.css" rel="stylesheet">

    <script type="text/javascript" src="//code.jquery.com/jquery-2.1.1.min.js"></script>
    <script type="text/javascript" src="//maxcdn.bootstrapcdn.com/bootstrap/3.3.1/js/bootstrap.min.js"></script>
    
    <script src="//cdnjs.cloudflare.com/ajax/libs/moment.js/2.9.0/moment-with-locales.js"></script>
    
    
    <script src="//cdn.rawgit.com/Eonasdan/bootstrap-datetimepicker/e8bddc60e73c1ec2475f827be36e1957af72e2ea/src/js/bootstrap-datetimepicker.js"></script>
    
    
    
</head>

<body>

<div class="navbar navbar-default navbar-fixed-top" role="navigation">
    <div class="container">

        <!-- Collapsed navigation -->
        <div class="navbar-header">
            <!-- Expander button -->
            <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>

            <!-- Main title -->
            <a class="navbar-brand" href=""></a>
        </div>

        <!-- Expanded navigation -->
        <div class="navbar-collapse collapse">
            <!-- Main navigation -->
            <ul class="nav navbar-nav">
            
            
                <li >
                    <a href="..">Usage</a>
                </li>
            
            
            
                <li >
                    <a href="../Installing/">Installing</a>
                </li>
            
            
            
                <li >
                    <a href="../Functions/">Functions</a>
                </li>
            
            
            
                <li class="active">
                    <a href="./">Options</a>
                </li>
            
            
            
                <li >
                    <a href="../Events/">Events</a>
                </li>
            
            
            
                <li >
                    <a href="../Changelog.md">Change Log</a>
                </li>
            
            
            
                <li >
                    <a href="../ContributorsGuide/">Dev Guide</a>
                </li>
            
            
            
                <li >
                    <a href="../Extras/">Extras</a>
                </li>
            
            
            
                <li >
                    <a href="../FAQ/">FAQs</a>
                </li>
            
            
            </ul>

            <!-- Search, Navigation and Repo links -->
            <ul class="nav navbar-nav navbar-right">
                
				
                
            </ul>
        </div>
    </div>
</div>

<div class="container">
    <div class="row">
        <div class="col-md-3"><script async type="text/javascript" src="//cdn.carbonads.com/carbon.js?serve=CK7DC5QN&placement=eonasdangithubio" id="_carbonads_js"></script>
<div class="bs-sidebar hidden-print affix well" role="complementary">    <ul class="nav bs-sidenav">
    
        <li class="main active"><a href="#options">Options</a></li>
        
            <li><a href="#options_1">options()</a></li>
        
            <li><a href="#optionsoptions">options([options])</a></li>
        
            <li><a href="#date">date</a></li>
        
            <li><a href="#format">format</a></li>
        
            <li><a href="#dayviewheaderformat">dayViewHeaderFormat</a></li>
        
            <li><a href="#extraformats">extraFormats</a></li>
        
            <li><a href="#stepping">stepping</a></li>
        
            <li><a href="#minmaxdate">min/maxDate</a></li>
        
            <li><a href="#usecurrent">useCurrent</a></li>
        
            <li><a href="#collapse">collapse</a></li>
        
            <li><a href="#locale">locale</a></li>
        
            <li><a href="#defaultdate">defaultDate</a></li>
        
            <li><a href="#endisableddates">en/disabledDates</a></li>
        
            <li><a href="#icons">icons</a></li>
        
            <li><a href="#usestrict">useStrict</a></li>
        
            <li><a href="#sidebyside">sideBySide</a></li>
        
            <li><a href="#daysofweekdisabled">daysOfWeekDisabled</a></li>
        
            <li><a href="#calendarweeks">calendarWeeks</a></li>
        
            <li><a href="#viewmode">viewMode</a></li>
        
            <li><a href="#toolbarplacement">toolbarPlacement</a></li>
        
            <li><a href="#showtodaybutton">showTodayButton</a></li>
        
            <li><a href="#showclear">showClear</a></li>
        
            <li><a href="#showclose">showClose</a></li>
        
            <li><a href="#widgetpositioning">widgetPositioning</a></li>
        
            <li><a href="#widgetparent">widgetParent</a></li>
        
            <li><a href="#keepopen">keepOpen</a></li>
        
            <li><a href="#inline">inline</a></li>
        
            <li><a href="#keepinvalid">keepInvalid</a></li>
        
            <li><a href="#keybinds">keyBinds</a></li>
        
            <li><a href="#debug">debug</a></li>
        
            <li><a href="#ignorereadonly">ignoreReadonly</a></li>
        
            <li><a href="#disabledtimeintervals">disabledTimeIntervals</a></li>
        
            <li><a href="#allowinputtoggle">allowInputToggle</a></li>
        
            <li><a href="#focusonshow">focusOnShow</a></li>
        
            <li><a href="#endisabledhours">en/disabledHours</a></li>
        
            <li><a href="#viewdate">viewDate</a></li>
        
            <li><a href="#parseinputdate">parseInputDate</a></li>
        
            <li><a href="#tooltips">tooltips</a></li>
        
            <li><a href="#timezone">timeZone</a></li>
        
    
    </ul>
</div></div>
        <div class="col-md-8" role="main">
            <div class="alert alert-danger" style="font-size:1.5em;">
                <strong>Important!</strong>
                Please read this <a href="https://eonasdan.com/posts/state-of-my-picker" target="_blank">blog post</a>
            </div>
            

<h2 id="options">Options</h2>
<div class="alert alert-info">
    <strong>Note</strong>
    All options are accessed via the <code>data</code> attribute e.g. <code>$('#datetimepicker').data("DateTimePicker").OPTION()</code>
</div>

<h3 id="options_1">options()</h3>
<p>Returns the components current options object. Note that the changing the values of the returned object does not change the components actual configuration. Use <code>options(options)</code> to set the components options massively or the other methods for setting config options individually.</p>
<h3 id="optionsoptions">options([options])</h3>
<p>Takes an object variable with option key:value properties and configures the component. Use this to update multiple options on the component.</p>
<hr />
<h3 id="date">date</h3>
<p>Returns the component's model current date, a <code>moment</code> object or <code>null</code> if not set.</p>
<h4 id="datenewdate">date([newDate])</h4>
<p>Takes <code>string, Date, moment, null</code> parameter and sets the components model current moment to it. Passing a <code>null</code> value unsets the components model current moment. Parsing of the newDate parameter is made using moment library with the <code>options.format</code> and <code>options.useStrict</code> components configuration.</p>
<h5 id="throws">Throws</h5>
<ul>
<li><code>TypeError</code> - in case the <code>newDate</code> cannot be parsed</li>
</ul>
<h5 id="emits">Emits</h5>
<ul>
<li><code>dp.change</code> - In case <code>newDate</code> is different from current moment</li>
</ul>
<hr />
<h3 id="format">format</h3>
<pre><code>Default: false
</code></pre>
<p>See <a href="http://momentjs.com/docs/#/displaying/format/">momentjs' docs</a> for valid formats. Format also dictates what components are shown, e.g. <code>MM/dd/YYYY</code> will not display the time picker.</p>
<h4 id="format_1">format()</h4>
<p>Returns the component's <code>options.format</code> <code>string</code></p>
<h4 id="formatformat">format(format)</h4>
<p>Takes a <a href="http://momentjs.com/docs/#/displaying/format/">moment.js</a> format <code>string</code> and sets the components  <code>options.format</code>. This is used for displaying and also for parsing input strings either from the input element the component is attached to or the <code>date()</code> function.
The parameter can also be a <code>boolean:false</code> in which case the format is set to the locale's <code>L LT</code>.</p>
<p><strong>Note:</strong> this is also used to determine if the TimePicker sub component will display the hours in 12 or 24 format. (if 'a' or 'h' exists in the passed <code>string</code> then a 12 hour mode is set)</p>
<hr />
<h3 id="dayviewheaderformat">dayViewHeaderFormat</h3>
<pre><code>Default: 'MMMM YYYY'
</code></pre>
<p>Changes the heading of the datepicker when in "days" view.</p>
<p><img alt="Datepicker Header" src="../img/dpheader.png" /></p>
<h4 id="dayviewheaderformat_1">dayViewHeaderFormat()</h4>
<p>Returns a <code>string</code> variable with the currently set <code>options.dayViewHeaderFormat</code> option.</p>
<h4 id="dayviewheaderformatstring">dayViewHeaderFormat(string)</h4>
<p>Takes a <code>string</code> value.</p>
<p>Used to customize the header of the day view.</p>
<hr />
<h3 id="extraformats">extraFormats</h3>
<pre><code>Default: false
</code></pre>
<p>Allows for several input formats to be valid. See <a href="https://github.com/Eonasdan/bootstrap-datetimepicker/pull/666">this PR</a>.</p>
<h4 id="extraformats_1">extraFormats()</h4>
<p>Returns a <code>boolean</code> or array with the <code>options.extraFormats</code> option configuration</p>
<h4 id="extraformatsformats">extraFormats(formats)</h4>
<p>Takes an array of valid input moment format options.</p>
<hr />
<h3 id="stepping">stepping</h3>
<pre><code>Default: 1
</code></pre>
<p>Number of minutes the up/down arrow's will move the minutes value in the time picker</p>
<h4 id="stepping_1">stepping()</h4>
<p>Returns a <code>number</code> with the <code>options.stepping</code> option configuration</p>
<h4 id="steppingnumber">stepping(number)</h4>
<p>Takes a <code>number</code>. This be the amount the up/down arrows move the minute value with a time picker.</p>
<hr />
<h3 id="minmaxdate">min/maxDate</h3>
<pre><code>Default: false
Accepts: date, moment, string
</code></pre>
<p>Prevents date/time selections before this date. Will override <code>defaultDate</code> and <code>useCurrent</code> if either of these settings are the same day since both options are invalid according to the rules you've selected.</p>
<h4 id="mindate">minDate()</h4>
<p>Returns the currently set moment of the <code>options.minDate</code> or <code>false</code> if not set</p>
<h4 id="mindatemindate">minDate(minDate)</h4>
<p>Takes a minDate <code>string, Date, moment, boolean:false</code> parameter and disallows the user to select a moment that is before that moment. If a <code>boolean:false</code> value is passed the <code>options.minDate</code> parameter is cleared and there is no restriction to the miminum moment the user can select. </p>
<p><strong>Note:</strong> If the minDate parameter is after the currently selected moment the currently selected moment changes to minDate parameter</p>
<h5 id="throws_1">Throws</h5>
<ul>
<li>
<p><code>TypeError</code> - if minDate parameter cannot be parsed using the <code>options.format</code> and <code>options.useStrict</code> configuration settings</p>
</li>
<li>
<p><code>TypeError</code> - if minDate parameter is after <code>options.maxDate</code></p>
</li>
</ul>
<h5 id="emits_1">Emits</h5>
<ul>
<li>
<p><code>dp.change</code> - if the new minDate is after currently selected moment (waiting for #472 to close in order to finalize this part)</p>
</li>
<li>
<p><code>dp.error</code> - if the new minDate is after currently selected moment (waiting for #472 to close in order to finalize this part)</p>
</li>
</ul>
<hr />
<h4 id="maxdate">maxDate()</h4>
<p>Returns the currently set moment of the <code>options.maxDate</code> or <code>false</code> if not set</p>
<h4 id="maxdatemaxdate">maxDate(maxDate)</h4>
<p>Takes a maxDate <code>string, Date, moment, boolean:false</code> parameter and disallows the user to select a moment that is after that moment. If a <code>boolean:false</code> value is passed <code>options.maxDate</code> is cleared and there is no restriction to the maximum moment the user can select.</p>
<p><strong>Note:</strong> If maxDate is before the currently selected moment the currently selected moment changes to maxDate</p>
<h5 id="throws_2">Throws</h5>
<ul>
<li>
<p><code>TypeError</code> - if maxDate parameter cannot be parsed using the <code>options.format</code> and <code>options.useStrict</code> configuration settings</p>
</li>
<li>
<p><code>TypeError</code> - if maxDate parameter is before <code>options.minDate</code></p>
</li>
</ul>
<h5 id="emits_2">Emits</h5>
<ul>
<li>
<p><code>dp.change</code> - if the new maxDate is after currently selected moment  (waiting for #472 to close in order to finalize this part)</p>
</li>
<li>
<p><code>dp.error</code> - if the new maxDate is after currently selected moment  (waiting for #472 to close in order to finalize this part)</p>
</li>
</ul>
<hr />
<h3 id="usecurrent">useCurrent</h3>
<pre><code>Default: true
</code></pre>
<p>On <code>show</code>, will set the picker to the current date/time.</p>
<h4 id="usecurrent_1">useCurrent()</h4>
<p>Returns a <code>boolean</code> or <code>string</code> with the <code>options.useCurrent</code> option configuration</p>
<h4 id="usecurrentboolean-or-string">useCurrent(boolean or string)</h4>
<p>Takes a <code>boolean</code> or <code>string</code>. If a <code>boolean</code> true is passed and the components model moment is not set (either through <code>setDate</code> or through a valid value on the input element the component is attached to) then the first time the user opens the datetimepicker widget the value is initialized to the current moment of the action. If a false <code>boolean</code> is passed then no initialization happens on the input element. You can select the granularity on the initialized moment by passing one of the following strings (<code>'year', 'month', 'day', 'hour', 'minute'</code>) in the variable.</p>
<p>If for example you pass <code>'day'</code> to the <code>setUseCurrent</code> function and the input field is empty the first time the user opens the datetimepicker widget the input text will be initialized to the current datetime with day granularity (ie if currentTime = <code>2014-08-10 13:32:33</code> the input value will be initialized to <code>2014-08-10 00:00:00</code>)</p>
<p><strong>Note:</strong> If the <code>options.defaultDate</code> is set or the input element the component is attached to has already a value that takes precedence and the functionality of <code>useCurrent</code> is not triggered!</p>
<hr />
<h3 id="collapse">collapse</h3>
<pre><code>Default: true
</code></pre>
<p>Using a Bootstraps collapse to switch between date/time pickers.</p>
<h4 id="collapse_1">collapse()</h4>
<p>Returns a <code>boolean</code> of the <code>options.sideBySide</code>.</p>
<h4 id="collapsecollapse">collapse(collapse)</h4>
<p>Takes a <code>boolean</code>. If set to <code>false</code> the picker will display similar to <code>sideBySide</code> except vertical.</p>
<hr />
<h3 id="locale">locale</h3>
<pre><code>Default: moment.locale()
Accepts: string, moment.locale('locale')
</code></pre>
<p>See <a href="https://github.com/moment/moment/tree/develop/locale">momentjs</a> for valid locales.</p>
<p>You must include <code>moment-with-locales.js</code> or a local js file.</p>
<h4 id="locale_1">locale()</h4>
<p>Returns the currently set locale of the <code>options.locale</code></p>
<h4 id="localenewlocale">locale(newLocale)</h4>
<p>Takes a <code>string</code> of any valid <a href="https://github.com/moment/moment/tree/develop/locale">moment locale</a> e.g. <code>de</code> for German.</p>
<h5 id="throws_3">Throws</h5>
<ul>
<li><code>TypeError</code> - if the locale is not loaded via a separate script or <code>moment-with-locales</code></li>
</ul>
<hr />
<h3 id="defaultdate">defaultDate</h3>
<pre><code>Default: false
Accepts: date, moment, string
</code></pre>
<p>Sets the picker default date/time. Overrides <code>useCurrent</code></p>
<h4 id="defaultdate_1">defaultDate()</h4>
<p>Returns a <code>moment</code> with the <code>options.defaultDate</code> option configuration or <code>false</code> if not set</p>
<h4 id="defaultdatedefaultdate">defaultDate(defaultDate)</h4>
<p>Takes a <code>string, Date, moment, boolean:false</code>. Will set the picker's inital date. If a <code>boolean:false</code> value is passed the <code>options.defaultDate</code> parameter is cleared. </p>
<ul>
<li>
<p><code>TypeError</code> - if the provided date pass validation, including <code>disabledDates</code>, <code>enabledDates</code>, <code>minDate</code>, <code>maxDate</code>, and <code>daysOfWeekDisabled</code></p>
</li>
<li>
<p><code>TypeError</code> - if the provided date cannot be parsed by momentjs</p>
</li>
</ul>
<hr />
<h3 id="endisableddates">en/disabledDates</h3>
<pre><code>Default: false
Accepts: array of [date, moment, string]
</code></pre>
<h4 id="disableddates">disabledDates()</h4>
<p>Returns an array with the currently set disabled dates on the component.</p>
<h4 id="disableddatesdates">disabledDates(dates)</h4>
<p>Takes an <code>[</code> <code>string</code> or <code>Date</code> or <code>moment</code> <code>]</code> of values and disallows the user to select those days. Setting this takes precedence over <code>options.minDate</code>, <code>options.maxDate</code> configuration. Also calling this function removes the configuration of options.enabledDates if such exist.</p>
<p><strong>Note:</strong> These values are matched with <code>Day</code> granularity.</p>
<hr />
<h4 id="enableddates">enabledDates()</h4>
<p>Returns an array with the currently set enabled dates on the component.</p>
<h4 id="enableddatesdates">enabledDates(dates)</h4>
<p>Takes an <code>[</code> <code>string</code> or <code>Date</code> or <code>moment</code> <code>]</code> of values and allows the user to select only from those days. Setting this takes precedence over <code>options.minDate</code>, <code>options.maxDate</code> configuration. Also calling this function removes the configuration of options.disabledDates if such exist.</p>
<p><strong>Note:</strong> These values are matched with <code>Day</code> granularity.</p>
<hr />
<h3 id="icons">icons</h3>
<pre><code>Default: {
            time: 'glyphicon glyphicon-time',
            date: 'glyphicon glyphicon-calendar',
            up: 'glyphicon glyphicon-chevron-up',
            down: 'glyphicon glyphicon-chevron-down',
            previous: 'glyphicon glyphicon-chevron-left',
            next: 'glyphicon glyphicon-chevron-right',
            today: 'glyphicon glyphicon-screenshot',
            clear: 'glyphicon glyphicon-trash',
            close: 'glyphicon glyphicon-remove'
        }
Accepts: object with all or some of the parameters above
</code></pre>
<p>Change the default icons for the pickers functions.</p>
<h4 id="icons_1">icons()</h4>
<p>Returns an <code>Ojbect</code> of <code>options.icons</code> </p>
<h4 id="iconsicons">icons(icons)</h4>
<p>Takes an <code>Ojbect</code> of <code>strings</code>.</p>
<h5 id="throws_4">Throws</h5>
<ul>
<li><code>TypeError</code> - if icons parameter is not an <code>Ojbect</code></li>
</ul>
<hr />
<h3 id="usestrict">useStrict</h3>
<pre><code>Default: false
</code></pre>
<p>Defines if moment should use strict date parsing when considering a date to be valid.</p>
<h4 id="usestrict_1">useStrict()</h4>
<p>Returns a <code>boolean</code> of the <code>options.useStrict</code></p>
<h4 id="usestrictusestrict">useStrict(useStrict)</h4>
<p>Takes a <code>boolean</code>. If <code>useStrict</code> is <code>true</code>, momentjs parsing rules will be stricter when determining if a date is valid or not.</p>
<hr />
<h3 id="sidebyside">sideBySide</h3>
<pre><code>Default: false
</code></pre>
<p>Shows the picker side by side when using the time and date together.</p>
<p><img alt="SideBySide" src="../img/sideBySide.png" /></p>
<h4 id="sidebyside_1">sideBySide()</h4>
<p>Returns a <code>boolean</code> of the <code>options.sideBySide</code>.</p>
<h4 id="sidebysidesidebyside">sideBySide(sideBySide)</h4>
<p>Takes a <code>boolean</code>. If <code>sideBySide</code> is <code>true</code> and the time picker is used, both components will display side by side instead of collapsing.</p>
<hr />
<h3 id="daysofweekdisabled">daysOfWeekDisabled</h3>
<pre><code>Default: []
Accepts: array of numbers from 0-6
</code></pre>
<p>Disables the section of days of the week, e.g. weekends.</p>
<h4 id="daysofweekdisabled_1">daysOfWeekDisabled()</h4>
<p>Returns an array with the <code>options.daysOfWeekDisabled</code> configuration setting of the component.</p>
<h4 id="daysofweekdisableddaysofweek">daysOfWeekDisabled(daysOfWeek)</h4>
<p>Takes an <code>[</code> <code>Number</code>:<code>0</code> to <code>6</code> <code>]</code> and disallow the user to select weekdays that exist in this array. This has lower priority over the <code>options.minDate</code>, <code>options.maxDate</code>, <code>options.disabledDates</code> and <code>options.enabledDates</code> configuration settings.</p>
<h5 id="emits_3">Emits</h5>
<ul>
<li>
<p><code>dp.change</code> - if the currently selected moment falls in the values passed on the daysOfWeek parameter. (waiting for #472 to close in order to finalize this part)</p>
</li>
<li>
<p><code>dp.error</code> - if the currently selected moment falls in the values passed on the daysOfWeek parameter. (waiting for #472 to close in order to finalize this part)</p>
</li>
</ul>
<hr />
<h3 id="calendarweeks">calendarWeeks</h3>
<pre><code>Default: false
</code></pre>
<p>Shows the week of the year to the left of first day of the week.</p>
<p><img alt="calendarWeek" src="../img/calendarWeeks.png" /></p>
<h4 id="calendarweeks_1">calendarWeeks()</h4>
<p>Returns a <code>boolean</code> with the current <code>options.calendarWeeks</code> option configuration</p>
<h4 id="calendarweeksboolean">calendarWeeks(boolean)</h4>
<p>Takes a <code>boolean</code> variable to set if the week numbers will appear to the left on the days view</p>
<hr />
<h3 id="viewmode">viewMode</h3>
<pre><code>Default: 'days'
Accepts: 'decades','years','months','days'
</code></pre>
<p>The default view to display when the picker is shown.</p>
<p><strong>Note</strong>: To limit the picker to selecting, for instance the year and month, use <code>format: MM/YYYY</code>.</p>
<h4 id="viewmode_1">viewMode()</h4>
<p>Returns a <code>string</code> of the <code>options.viewMode</code>.</p>
<h4 id="viewmodenewviewmode">viewMode(newViewMode)</h4>
<p>Takes a <code>string</code>. Valid values are <code>'days'</code>, <code>'months'</code>, <code>'years'</code> and <code>'decades'</code></p>
<h5 id="throws_5">Throws</h5>
<ul>
<li><code>TypeError</code> - if <code>newViewMode</code> parameter is not an a <code>string</code> or if <code>newViewMode</code> is not a valid value.</li>
</ul>
<hr />
<h3 id="toolbarplacement">toolbarPlacement</h3>
<pre><code>Default: 'default'
Accepts: 'default', 'top', 'bottom'
</code></pre>
<p>Changes the placement of the icon toolbar.</p>
<p><img alt="toolbarPlacement" src="../img/toolbarPlacement.png" /></p>
<h4 id="toolbarplacement_1">toolbarplacement()</h4>
<p>Returns a <code>string</code> variable with the currently set <code>options.toolbarplacement</code> option.</p>
<h4 id="toolbarplacementstring">toolbarplacement(string)</h4>
<p>Takes a <code>string</code> value. Valid values are <code>'default'</code>, <code>'top'</code> and <code>'bottom'</code>.</p>
<p>Changes the placement of the toolbar where the today, clear, component switch icon are located.</p>
<hr />
<h3 id="showtodaybutton">showTodayButton</h3>
<pre><code>Default: false
</code></pre>
<p>Show the "Today" button in the icon toolbar.</p>
<p>Clicking the "Today" button will set the calendar view and set the date to <code>now</code>.</p>
<h4 id="showtodaybutton_1">showTodayButton()</h4>
<p>Returns a <code>boolean</code> variable with the currently set <code>options.showTodayButton</code> option.</p>
<h4 id="showtodaybuttonboolean">showTodayButton(boolean)</h4>
<p>Takes a <code>boolean</code> variable to set if the Today button will appear on the widget</p>
<hr />
<h3 id="showclear">showClear</h3>
<pre><code>Default: false
</code></pre>
<p>Show the "Clear" button in the icon toolbar.</p>
<p>Clicking the "Clear" button will set the calendar to null.</p>
<h4 id="showclear_1">showClear()</h4>
<p>Returns a <code>boolean</code> variable with the currently set <code>options.showClear</code> option.</p>
<h4 id="showclearboolean">showClear(boolean)</h4>
<p>Takes a <code>boolean</code> variable to set if the clear date button will appear on the widget</p>
<hr />
<h3 id="showclose">showClose</h3>
<pre><code>Default: false
</code></pre>
<p>Show the "Close" button in the icon toolbar.</p>
<p>Clicking the "Close" button will call <code>hide()</code></p>
<h4 id="showclose_1">showClose()</h4>
<p>Returns a <code>boolean</code> variable with the currently set <code>options.showClose</code> option.</p>
<h4 id="showcloseboolean">showClose(boolean)</h4>
<p>Takes a <code>boolean</code> value.</p>
<p>If <code>true</code>, an icon will be displayed on the toolbar that will hide the picker</p>
<hr />
<h3 id="widgetpositioning">widgetPositioning</h3>
<pre><code>Default: {
            horizontal: 'auto'
            vertical: 'auto'
         }
Accepts: object with the all or one of the parameters above
         horizontal: 'auto', 'left', 'right'
         vertical: 'auto', 'top', 'bottom'
</code></pre>
<h4 id="widgetpositioning_1">widgetPositioning()</h4>
<p>Returns the currently set <code>options.widgetPositioning</code> object containing two keys <code>horizontal</code> and <code>vertical</code></p>
<h4 id="widgetpositioningpositioningobject">widgetPositioning(positioningObject)</h4>
<p>Takes an object parameter that can contain two keys <code>vertical</code> and <code>horizontal</code> each having a value of <code>'auto', 'top', 'bottom'</code> for <code>vertical</code> and <code>'auto', 'left', 'right'</code> for <code>horizontal</code> which defines where the dropdown with the widget will appear relative to the input element the component is attached to.</p>
<p><code>'auto'</code> is the default value for both <code>horizontal</code> and <code>vertical</code> keys and it tries to automatically place the dropdown in a position that is visible to the user. Usually you should not override those options unless you have a special need in your layout.</p>
<hr />
<h3 id="widgetparent">widgetParent</h3>
<pre><code>Default: null
Accepts: string or jQuery object
</code></pre>
<p>On picker show, places the widget at the identifier (string) or jQuery object <strong>if</strong> the element has css <code>position: 'relative'</code></p>
<h4 id="widgetparent_1">widgetParent()</h4>
<p>Returns a <code>$(element)</code> variable with the currently set <code>options.widgetParent</code> option.</p>
<h4 id="widgetparentwidgetparent">widgetParent(widgetParent)</h4>
<p>Takes a <code>string</code> or <code>$(element)</code> value.</p>
<hr />
<h3 id="keepopen">keepOpen</h3>
<pre><code>Default: false
</code></pre>
<p>Will cause the date picker to stay open after selecting a date.</p>
<h4 id="keepopen_1">keepOpen()</h4>
<p>Returns a <code>boolean</code> variable with the currently set <code>options.keepOpen</code> option.</p>
<h4 id="keepopenboolean">keepOpen(boolean)</h4>
<p>Takes a <code>boolean</code> value.</p>
<hr />
<h3 id="inline">inline</h3>
<pre><code>Default: false
</code></pre>
<p>Will display the picker inline without the need of a input field. This will also hide borders and shadows.</p>
<h4 id="inline_1">inline()</h4>
<p>Returns a <code>boolean</code> variable with the currently set <code>options.inline</code> option.</p>
<h4 id="inlineboolean">inline(boolean)</h4>
<p>Takes a <code>boolean</code> value.</p>
<hr />
<h3 id="keepinvalid">keepInvalid</h3>
<p><small>4.7.14</small></p>
<pre><code>Default: false
</code></pre>
<p>Will cause the date picker to <strong>not</strong> revert or overwrite invalid dates.</p>
<h4 id="keepinvalid_1">keepInvalid()</h4>
<p>Returns a <code>string</code> variable with the currently set <code>options.keepInvalid</code> option.</p>
<h4 id="keepinvalidboolean">keepInvalid(boolean)</h4>
<p>Takes a <code>boolean</code> value.</p>
<p>If <code>true</code>, invalid dates will not be reverted to a previous selection or changed.</p>
<hr />
<h3 id="keybinds">keyBinds</h3>
<pre><code>Default: up: function (widget) {
            if (widget.find('.datepicker').is(':visible')) {
                this.date(this.date().clone().subtract(7, 'd'));
            } else {
                this.date(this.date().clone().add(1, 'm'));
            }
        },
        down: function (widget) {
            if (!widget) {
                this.show();
            }
            else if (widget.find('.datepicker').is(':visible')) {
                this.date(this.date().clone().add(7, 'd'));
            } else {
                this.date(this.date().clone().subtract(1, 'm'));
            }
        },
        'control up': function (widget) {
            if (widget.find('.datepicker').is(':visible')) {
                this.date(this.date().clone().subtract(1, 'y'));
            } else {
                this.date(this.date().clone().add(1, 'h'));
            }
        },
        'control down': function (widget) {
            if (widget.find('.datepicker').is(':visible')) {
                this.date(this.date().clone().add(1, 'y'));
            } else {
                this.date(this.date().clone().subtract(1, 'h'));
            }
        },
        left: function (widget) {
            if (widget.find('.datepicker').is(':visible')) {
                this.date(this.date().clone().subtract(1, 'd'));
            }
        },
        right: function (widget) {
            if (widget.find('.datepicker').is(':visible')) {
                this.date(this.date().clone().add(1, 'd'));
            }
        },
        pageUp: function (widget) {
            if (widget.find('.datepicker').is(':visible')) {
                this.date(this.date().clone().subtract(1, 'M'));
            }
        },
        pageDown: function (widget) {
            if (widget.find('.datepicker').is(':visible')) {
                this.date(this.date().clone().add(1, 'M'));
            }
        },
        enter: function () {
            this.hide();
        },
        escape: function () {
            this.hide();
        },
        'control space': function (widget) {
            if (widget.find('.timepicker').is(':visible')) {
                widget.find('.btn[data-action="togglePeriod"]').click();
            }
        },
        t: function () {
            this.date(moment());
        },
        'delete': function () {
            this.clear();
        }
</code></pre>
<p>Allows for custom events to fire on keyboard press.</p>
<h4 id="keybinds_1">keyBinds()</h4>
<p>Returns a <code>string</code> variable with the currently set <code>options.keyBinds</code> option.</p>
<h4 id="keybindsobject">keyBinds(object)</h4>
<p>Takes an <code>object</code> value.</p>
<p>Allows for several keyBinding functions to be specified for ease of access or accessibility. See the options page for defaults.</p>
<hr />
<h3 id="debug">debug</h3>
<p><small>4.7.14</small></p>
<pre><code>Default: false
</code></pre>
<p>Will cause the date picker to stay open after a <code>blur</code> event.</p>
<hr />
<h3 id="ignorereadonly">ignoreReadonly</h3>
<p><small>4.7.14</small></p>
<pre><code>Default: false
</code></pre>
<p>Allow date picker show event to fire even when the associated input element has the <code>readonly="readonly"</code>property.</p>
<h4 id="ignorereadonly_1">ignoreReadonly()</h4>
<p>Returns a <code>boolean</code> variable with the currently set <code>options.ignoreReadonly</code> option.</p>
<h4 id="ignorereadonlyboolean">ignoreReadonly(boolean)</h4>
<p>Takes a <code>boolean</code> value.</p>
<p>Set this to <code>true</code> to allow the picker to be used even if the input field is <code>readonly</code>. This will <strong>not</strong> bypass the <code>disabled</code> property</p>
<hr />
<h3 id="disabledtimeintervals">disabledTimeIntervals</h3>
<p><small>4.14.30</small></p>
<pre><code>Default: false
</code></pre>
<p>Disables time selection between the given <code>moments</code>.</p>
<h4 id="disabledtimeintervals_1">disabledTimeIntervals()</h4>
<p>Returns an <code>array</code> variable with the currently set <code>options.disabledTimeIntervals</code> option.</p>
<h4 id="disabledtimeintervalsarray">disabledTimeIntervals(array)</h4>
<p>Takes a <code>array</code> value.</p>
<p>The array <strong>must</strong> be in the following format <code>[moment(),moment()]</code></p>
<p>For example:</p>
<pre><code>disabledTimeIntervals: [[moment({ h: 0 }), moment({ h: 8 })], [moment({ h: 18 }), moment({ h: 24 })]]
</code></pre>
<p>Will disable times between 12-8am and 6-12pm today</p>
<hr />
<h3 id="allowinputtoggle">allowInputToggle</h3>
<p><small>4.14.30</small></p>
<pre><code>Default: false
</code></pre>
<p>If <code>true</code>, the picker will show on textbox focus and icon click when used in a button group.</p>
<h4 id="allowinputtoggle_1">allowInputToggle()</h4>
<p>Returns a <code>boolean</code> variable with the currently set <code>options.allowInputToggle</code> option.</p>
<h4 id="allowinputtoggleboolean">allowInputToggle(boolean)</h4>
<p>Takes a <code>boolean</code> value.</p>
<p>If <code>true</code>, the picker will show on textbox focus and icon click when used in a button group</p>
<hr />
<h3 id="focusonshow">focusOnShow</h3>
<p><small>4.14.30</small></p>
<pre><code>Default: true
</code></pre>
<p>If <code>false</code>, the textbox will not be given focus when the picker is shown.</p>
<h4 id="focusonshow_1">focusOnShow()</h4>
<p>Returns a <code>boolean</code> variable with the currently set <code>options.focusOnShow</code> option.</p>
<h4 id="focusonshowboolean">focusOnShow(boolean)</h4>
<p>Takes a <code>boolean</code> value.</p>
<p>If <code>false</code>, the textbox will not be given focus when the picker is shown</p>
<hr />
<h3 id="endisabledhours">en/disabledHours</h3>
<p><small>4.14.30</small> Issue: #851</p>
<pre><code>Default: false
</code></pre>
<h4 id="disabledhours">disabledHours()</h4>
<p>Returns an <code>array</code> variable with the currently set <code>options.en/disabledHours</code> option.</p>
<h4 id="disabledhoursboolean">disabledHours(boolean)</h4>
<p>Takes a <code>array</code> value.</p>
<p>Must be in 24 hour format. Will allow or disallow hour selections (much like <code>disabledTimeIntervals</code>) but will affect all days.</p>
<p>Like <code>en/disabledDates</code>, these options are mutually exclusive and will reset one of the options back to false.</p>
<pre><code>disabledHours: [0, 1, 2, 3, 4, 5, 6, 7, 8, 18, 19, 20, 21, 22, 23, 24]
enabledHours: [9, 10, 11, 12, 13, 14, 15, 16]
</code></pre>
<hr />
<h3 id="viewdate">viewDate</h3>
<p><small>4.14.30</small></p>
<pre><code>Default: false
</code></pre>
<p>This will change the <code>viewDate</code> without changing or setting the selected date.</p>
<hr />
<h3 id="parseinputdate">parseInputDate</h3>
<p><small>4.14.30</small> Issue #1095</p>
<h4 id="parseinputdate_1">parseInputDate()</h4>
<p>Returns a <code>function</code> with the currently set <code>options.parseInputDate</code></p>
<h4 id="parseinputdatefunction">parseInputDate(function)</h4>
<p>Takes a <code>function</code></p>
<p>Allows custom input formatting For example: the user can enter 'yesterday' or '30 days ago'.</p>
<p>Example:</p>
<pre><code>var parseRelativeDate = function(relativeDate) {

    switch (relativeDate) {
        case 'today':
            return moment()
        case 'yesterday':
            return moment().subtract(1, 'day');
        default:
            return moment()
                .subtract(Number(relativeDate.replace(&quot;days ago&quot;, &quot;&quot;).trim()), 'days');
    }
}

var parseInputDate = function(inputDate) {
    var relativeDatePattern = /today|yesterday|[0-9]+\s+(days ago)/,
        resultDate;

    if (moment.isMoment(inputDate) || inputDate instanceof Date) {
        resultDate = moment(inputDate);
    } else {
        var relativeDate = inputDate.match(relativeDatePattern),
            parseDate = null;

        if (relativeDate !== null) 
          parseDate = this.parseRelativeDate(inputDate.match(relativeDatePattern)[0]);
        else 
          parseDate = moment();

        resultDate = moment(parseDate, &quot;YYYY-MM-DD&quot;);
    }

    return resultDate;
}
</code></pre>

<hr />
<h3 id="tooltips">tooltips</h3>
<p><small>4.15.35</small></p>
<pre><code>tooltips: {
    today: 'Go to today',
    clear: 'Clear selection',
    close: 'Close the picker',
    selectMonth: 'Select Month',
    prevMonth: 'Previous Month',
    nextMonth: 'Next Month',
    selectYear: 'Select Year',
    prevYear: 'Previous Year',
    nextYear: 'Next Year',
    selectDecade: 'Select Decade',
    prevDecade: 'Previous Decade',
    nextDecade: 'Next Decade',
    prevCentury: 'Previous Century',
    nextCentury: 'Next Century'
}
</code></pre>

<p>This will change the <code>tooltips</code> over each icon to a custom string.</p>
<h4 id="tooltips_1">tooltips()</h4>
<p>Returns an <code>Ojbect</code> of <code>options.tooltips</code> </p>
<h4 id="tooltipstooltips">tooltips(tooltips)</h4>
<p>Takes an <code>Ojbect</code> of <code>strings</code>.</p>
<h5 id="throws_6">Throws</h5>
<ul>
<li><code>TypeError</code> - if tooltips parameter is not an <code>Ojbect</code></li>
</ul>
<hr />
<h3 id="timezone">timeZone</h3>
<p><small>4.17.37</small></p>
<pre><code>timeZone: ''
</code></pre>

<p>Allows the setting of the Time Zone. You must include <a href="http://momentjs.com/timezone/"><code>moment-timezone.js</code></a> and <code>moment-timzone</code> data. See moment timezone documentation for usage.</p>
<h4 id="timezone_1">timeZone()</h4>
<p>Returns an <code>string</code> of <code>options.timeZone</code> </p>
<h4 id="timezonetimezone">timeZone(timeZone)</h4>
<p>Takes an <code>string</code> of a valid timezone.</p>
<h5 id="throws_7">Throws</h5>
<ul>
<li><code>TypeError</code> - if tooltips parameter is not an <code>string</code></li>
</ul>
        </div>
    </div>
</div>



<script src="../js/prettify-1.0.min.js"></script>
<script src="../js/base.js"></script>
<script>
    if (top != self) {
        top.location.replace(self.location.href);
    }
    (function (i, s, o, g, r, a, m) {
        i['GoogleAnalyticsObject'] = r;
        i[r] = i[r] || function () {
            (i[r].q = i[r].q || []).push(arguments)
        }, i[r].l = 1 * new Date();
        a = s.createElement(o),
            m = s.getElementsByTagName(o)[0];
        a.async = 1;
        a.src = g;
        m.parentNode.insertBefore(a, m)
    })(window, document, 'script', '//www.google-analytics.com/analytics.js', 'ga');

    ga('create', 'UA-47462200-1', 'eonasdan.github.io');
    ga('send', 'pageview');
</script>
</body>
</html>