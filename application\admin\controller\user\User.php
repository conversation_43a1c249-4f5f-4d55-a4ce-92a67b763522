<?php

namespace app\admin\controller\user;

use app\common\controller\Backend;
use app\common\library\Auth;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\Reader\Xls;
use PhpOffice\PhpSpreadsheet\Reader\Csv;
use Exception;
use think\exception\PDOException;
use app\admin\model\UserGroup;

/**
 * 会员管理
 *
 * @icon fa fa-user
 */
class User extends Backend
{

    protected $relationSearch = true;
    protected $searchFields = 'id,username,nickname';

    /**
     * @var \app\admin\model\User
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\User;
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $extra = $this->request->get("extra", '');
            $whereExtra = [];
            if($extra){
                $extraArr = json_decode($extra,true);
                if($extraArr)
                {
                    if(strpos($extraArr['module_id'],'_')!==false)
                    {
                        $extraArrNew = explode('_',$extraArr['module_id']);
                        if(!empty($extraArrNew[1]))
                        {
                            $whereExtra['org_id'] = $extraArrNew[1];
                        }
                    }elseif(!empty($extraArr['module_id'])){
                        $whereExtra['customer_id'] = $extraArr['module_id'];
                    }
                }
            }
            $list = $this->model
                ->with('group')
                ->where($where)
                ->where($whereExtra)
                ->order($sort, $order)
                ->paginate($limit);
            foreach ($list as $k => $v) {
                $v->avatar = $v->avatar ? cdnurl($v->avatar, true) : letter_avatar($v->nickname);
                $v->hidden(['password', 'salt']);
            }
            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $this->token();
        }
        $this->view->assign('groupList', build_select('row[group_id]', \app\admin\model\UserGroup::column('id,name'), '', ['class' => 'form-control selectpicker']));
        return parent::add();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        if ($this->request->isPost()) {
            $this->token();
        }
        $row = $this->model->get($ids);
        // $org_id = $row['org_id'];
        // $org_arr = [0=>$org_id];
        // for($i=1;$i<5;$i++)
        // {
        //     $parent_id = \app\admin\model\system\Org::where('org_id',$org_id)->value('parent_id');
        //     if(empty($parent_id)){
        //         continue;
        //     }
        //     $org_arr[$i] = $parent_id;
        //     $org_id = $parent_id;
        // }
        // $row['org_arr'] = $org_arr;
        $this->modelValidate = true;
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $this->view->assign('groupList', build_select('row[group_id]', \app\admin\model\UserGroup::column('id,name'), $row['group_id'], ['class' => 'form-control selectpicker']));
        return parent::edit($ids);
    }

    /**
     * 删除
     */
    public function del($ids = "")
    {
        if (!$this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ? $ids : $this->request->post("ids");
        $row = $this->model->get($ids);
        $this->modelValidate = true;
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        Auth::instance()->delete($row['id']);
        $this->success();
    }

    /**
     * 导入
     *
     * @return void
     * @throws PDOException
     * @throws BindParamException
     */
    public function import()
    {
        $file = $this->request->request('file');
        if (!$file) {
            $this->error(__('Parameter %s can not be empty', 'file'));
        }
        $filePath = ROOT_PATH . DS . 'public' . DS . $file;
        if (!is_file($filePath)) {
            $this->error(__('No results were found'));
        }
        //实例化reader
        $ext = pathinfo($filePath, PATHINFO_EXTENSION);
        if (!in_array($ext, ['csv', 'xls', 'xlsx'])) {
            $this->error(__('Unknown data format'));
        }
        if ($ext === 'csv') {
            $file = fopen($filePath, 'r');
            $filePath = tempnam(sys_get_temp_dir(), 'import_csv');
            $fp = fopen($filePath, 'w');
            $n = 0;
            while ($line = fgets($file)) {
                $line = rtrim($line, "\n\r\0");
                $encoding = mb_detect_encoding($line, ['utf-8', 'gbk', 'latin1', 'big5']);
                if ($encoding !== 'utf-8') {
                    $line = mb_convert_encoding($line, 'utf-8', $encoding);
                }
                if ($n == 0 || preg_match('/^".*"$/', $line)) {
                    fwrite($fp, $line . "\n");
                } else {
                    fwrite($fp, '"' . str_replace(['"', ','], ['""', '","'], $line) . "\"\n");
                }
                $n++;
            }
            fclose($file) || fclose($fp);

            $reader = new Csv();
        } elseif ($ext === 'xls') {
            $reader = new Xls();
        } else {
            $reader = new Xlsx();
        }
        //加载文件
        $insert = [];
        $num = $insert_num = $update_num = 0;
        $errorArr = [];
        try {
            if (!$PHPExcel = $reader->load($filePath)) {
                $this->error(__('Unknown data format'));
            }
            $currentSheet = $PHPExcel->getSheet(0);  //读取文件中的第一个工作表
            $allColumn = $currentSheet->getHighestDataColumn(); //取得最大的列号
            $allRow = $currentSheet->getHighestRow(); //取得一共有多少行
            $maxColumnNumber = Coordinate::columnIndexFromString($allColumn);
            $fields = [];
            for ($currentRow = 3; $currentRow <= 3; $currentRow++) {
                for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
                    $val = $currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();
                    $fields[] = $val;
                }
            }
            if($fields[0]!='序号' || $fields[6]!='一级'){
                throw new Exception('模版格式不正确!');
            }
            $auth = new Auth();
            for ($currentRow = 4; $currentRow <= $allRow; $currentRow++) {
                $values = [];
                $org_id = 0;
                for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
                    $val = $currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();
                    $values[] = is_null($val) ? '' : $val;
                }
                if(empty($values[0]))continue;
                $nickname = $values[1];
                $username = $values[2];
                $password = $values[3];
                $mobile = $values[4];
                $group_id = UserGroup::where('name', $values[5])->value('id');
                $customer_id = \app\admin\model\system\Customer::where('customer_name',$values[6])->value('customer_id');
                $org_name = '';
                for($i=7;$i<=15;$i++){
                    if(empty($values[$i])){
                        continue;
                    }
                    $org_name = $values[$i];
                }
                if(!empty($org_name))
                {
                    $org_id = \app\admin\model\system\Org::where(['customer_id'=> $customer_id,'org_name'=>$org_name])->value('org_id');
                }
                if(empty($nickname) || empty($username) || empty($password) || empty($mobile) || empty($group_id) || empty($customer_id)){
                    $errorArr[] = $currentRow;
                }else{
                    $insert[] = [
                        'username' => $username,
                        'nickname' => $nickname,
                        'password' => $password,
                        'mobile' => $mobile,
                        'customer_id' => $customer_id,
                        'org_id' => $org_id,
                        'group_id' => $group_id,
                    ];
                }
            }
        } catch (Exception $exception) {
            $this->error($exception->getMessage());
        }

        if($errorArr){
            $this->error("导入失败，第" . implode(',', $errorArr) . "行数据存在问题!");
        }

        if (!$insert) {
            $this->error(__('No rows were updated'));
        }

        try {
            foreach ($insert as $k => $v) {
                $nickname = $v['nickname'];
                $username = $v['username'];
                $password = $v['password'];
                $mobile = $v['mobile'];
                $group_id = $v['group_id'];
                $customer_id = $v['customer_id'];
                $org_id = $v['org_id'];
                $ret = $auth->register($username, $password, '', $mobile, ['nickname'=>$nickname,'group_id'=>$group_id, 'customer_id'=>$customer_id, 'org_id'=>$org_id]);
                if ($ret) {
                    $insert_num++;
                } else {
                    $update_num++;
                }
            }
            
        } catch (PDOException $exception) {
            $msg = $exception->getMessage();
            if (preg_match("/.+Integrity constraint violation: 1062 Duplicate entry '(.+)' for key '(.+)'/is", $msg, $matches)) {
                $msg = "导入失败，包含【{$matches[1]}】的记录已存在";
            };
            $this->error($msg);
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }

        $this->success("导入成功,总共{$num}条,新增{$insert_num}条,失败:{$update_num}条!");
    }

    /**
     * 批量迁移
     */
    public function migration(){
        $ids = request()->post('ids');
        $category = request()->post('category');
        $uidArr = explode(',', $ids);
        if(empty($uidArr) || empty($category)){
            return json(['code'=>0,'msg'=>'参数错误']);
        }
        $customer_id = $org_id = 0;
        if(strpos($category,'_')!==false)
        {
            $extraArrNew = explode('_',$category);
            if(!empty($extraArrNew[1]))
            {
                $org_id = $extraArrNew[1];
            }
            if(!empty($extraArrNew[0]))
            {
                $customer_id = $extraArrNew[0];
            }
        }elseif(!empty($category)){
            $customer_id = $category;
        }
        $this->model->where(['id'=>['in', $uidArr]])->update(['customer_id' => $customer_id,'org_id'=>$org_id]);
        return json(['code'=>1,'msg'=>'迁移成功']);
    }

    /**
     * 选择
     */
    public function select()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $extra = $this->request->get("extra", '');
            $whereExtra = [];
            if($extra){
                $extraArr = json_decode($extra,true);
                if($extraArr)
                {
                    if(strpos($extraArr['module_id'],'_')!==false)
                    {
                        $extraArrNew = explode('_',$extraArr['module_id']);
                        if(!empty($extraArrNew[1]))
                        {
                            $whereExtra['org_id'] = $extraArrNew[1];
                        }
                    }elseif(!empty($extraArr['module_id'])){
                        $whereExtra['customer_id'] = $extraArr['module_id'];
                    }
                }
            }
            $list = $this->model
                ->with('group')
                ->where($where)
                ->where($whereExtra)
                ->order($sort, $order)
                ->paginate($limit);
            foreach ($list as $k => $v) {
                $v->avatar = $v->avatar ? cdnurl($v->avatar, true) : letter_avatar($v->nickname);
                $v->hidden(['password', 'salt']);
            }
            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        $visibility = input("visibility") ?? 1;
        $this->view->assign("visibility", $visibility);
        return $this->view->fetch();
    }

}
