<?php

namespace app\api\controller;

use app\common\controller\Api;
use think\Db;

/**
 * 首页接口
 */
class Index extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    /**
     * 首页
     *
     */
    public function index()
    {
        $this->success('请求成功');
    }

    /**
     * 测试
     */
    public function test(){
        echo "123123123123";
        echo "456456";
        echo "789789";
        echo "123123789789";
        $list = Db::connect('aimaster')->name('user')->find();
        dump($list);

        
        $list = Db::connect('examadmin')->name('user')->find();
        dump($list);

        
        $list = Db::connect('vtrs')->name('user')->find();
        dump($list);

        
        $list = Db::connect('formmaster')->name('user')->find();
        dump($list);exit;
    }
}
