define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'user/user/index',
                    add_url: 'user/user/add',
                    edit_url: 'user/user/edit',
                    del_url: 'user/user/del',
                    multi_url: 'user/user/multi',
                    import_url: 'user/user/import',
                    table: 'user',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'user.id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id'), sortable: true},
                        {field: 'group.name', title: __('Group')},
                        {field: 'username', title: __('Username'), operate: 'LIKE'},
                        {field: 'nickname', title: __('Nickname'), operate: 'LIKE'},
                        // {field: 'email', title: __('Email'), operate: 'LIKE'},
                        {field: 'mobile', title: __('Mobile'), operate: 'LIKE'},
                        // {field: 'avatar', title: __('Avatar'), events: Table.api.events.image, formatter: Table.api.formatter.image, operate: false},
                        // {field: 'level', title: __('Level'), operate: 'BETWEEN', sortable: true},
                        // {field: 'gender', title: __('Gender'), visible: false, searchList: {1: __('Male'), 0: __('Female')}},
                        // {field: 'score', title: __('Score'), operate: 'BETWEEN', sortable: true},
                        // {field: 'successions', title: __('Successions'), visible: false, operate: 'BETWEEN', sortable: true},
                        // {field: 'maxsuccessions', title: __('Maxsuccessions'), visible: false, operate: 'BETWEEN', sortable: true},
                        // {field: 'logintime', title: __('Logintime'), formatter: Table.api.formatter.datetime, operate: 'RANGE', addclass: 'datetimerange', sortable: true},
                        // {field: 'loginip', title: __('Loginip'), formatter: Table.api.formatter.search},
                        // {field: 'jointime', title: __('Jointime'), formatter: Table.api.formatter.datetime, operate: 'RANGE', addclass: 'datetimerange', sortable: true},
                        // {field: 'joinip', title: __('Joinip'), formatter: Table.api.formatter.search},
                        // {field: 'status', title: __('Status'), formatter: Table.api.formatter.status, searchList: {normal: __('Normal'), hidden: __('Hidden')}},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ],queryParams: function (params) {
                    var extra = params.extra ? JSON.parse(params.extra) : {};
                    extra.module_id = Config.module_id;
                    params.extra = JSON.stringify(extra);
                    console.log(params);
                    return params;
                }
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            // 批量迁移
            $(document).on('click', '.btn-classify', function () {
                var ids = Table.api.selectedids(table);
                Layer.open({
                    title: '批量迁移',
                    area: ['80%', '80%'],
                    content: Template("typetpl", {}),
                    btn: [__('OK')],
                    yes: function (index, layero) {
                        console.log("Config.module_id1:",Config.module_id1);
                        console.log("ids:", ids.join(','));
                        if (Config.module_id1 == '' || Config.module_id1 == null || Config.module_id1==0) {
                            Layer.msg("请选择客户", {icon: 7});
                        }else{
                            Fast.api.ajax({
                                url: "user/user/migration",
                                type: "post",
                                data: {category: Config.module_id1, ids: ids.join(',')},
                            }, function () {
                                table.bootstrapTable('refresh', {});
                                Layer.close(index);
                            });
                        }
                    },
                    success: function (layero, index) {
                        // 提取公共的 jstree 初始化函数
                        function initJsTree(treeId, searchId, treeUrl) {
                            $(treeId).on("changed.jstree", function (e, data) {
                                console.log("data.selected:", data.selected);
                                var selectedStr = data.selected.join(",");
                                Config.module_id1 = selectedStr;
                                return false;
                            });
                            
                            $(document).on("keypress", searchId, function (e) {
                                if (e.which === 13) { // 回车键
                                    $(this).blur(); // 触发失去焦点事件
                                }
                            });
                            
                            $(document).on("blur", searchId, function () {
                                var query = $(this).val();
                                var tree = $(treeId).jstree(true);
                                
                                if (query.trim() !== '') {
                                    // 执行搜索
                                    tree.search(query);
                                } else {
                                    // 如果查询为空，清除搜索结果
                                    tree.clear_search();
                                }
                            });
                            
                            $(treeId).jstree({
                                "themes": {
                                    "stripes": false
                                },
                                "checkbox": {
                                    "keep_selected_style": false,
                                },
                                "types": {
                                    "course": {
                                        "icon": "fa fa-folder-open",
                                    },
                                    "unit": {
                                        "icon": "fa fa-th",
                                    },
                                    "lecture": {
                                        "icon": "fa fa-list",
                                    },
                                    "link": {
                                        "icon": "fa fa-link",
                                    },
                                    "disabled": {
                                        "check_node": false,
                                        "uncheck_node": false
                                    }
                                },
                                'plugins': ["types", "search"],
                                "core": {
                                    "multiple": true,
                                    'check_callback': true,
                                    "data": {
                                        'url' : treeUrl,
                                    }
                                }
                            });
                        }
            
                        // 初始化第一个树
                        require(['jstree'], function () {
                            initJsTree('#channeltree1', '#demo_q1', 'system/org/listTree');
                        });
                    }
                });
            });

            // jstree
            require(['jstree'], function () {
                
                var name = '';
                var tree_url = "system/org/listTree";
            
                //全选和展开
                // $(document).on("click", "#checkall", function () {
                //     $("#channeltree").jstree($(this).prop("checked") ? "check_all" : "uncheck_all");
                // });
                // $(document).on("click", "#expandall", function () {
                //     $("#channeltree").jstree($(this).prop("checked") ? "open_all" : "close_all");
                // });
                $('#channeltree').on("changed.jstree", function (e, data) {
                    console.log("data.selected:", data.selected);
                    var selectedStr = data.selected.join(",");
                    Config.module_id = selectedStr;
                    Controller.customer_id = 0;
                    Controller.org_id = 0;

                    // 检查是否存在下划线
                    if (selectedStr.indexOf('_') !== -1) {
                        // 存在下划线，将字符串转为数组
                        var selectedArray = selectedStr.split('_');
                        // 数组的第一个为customer_id
                        Controller.customer_id = selectedArray[0] || 0;
                        // org_id为最后一个
                        Controller.org_id = selectedArray[selectedArray.length - 1] || 0;
                    } else {
                        // 不存在下划线，customer_id为获取的值，org_id为空
                        Controller.customer_id = selectedStr || 0;
                        Controller.org_id = 0;
                    }
                    $("#customer_id").val(Controller.customer_id);
                    $("#org_id").val(Controller.org_id);
                    table.bootstrapTable('refresh', {});
                    return false;
                });
                $(document).on("keypress", "#demo_q", function (e) {
                    if (e.which === 13) { // 回车键
                        $(this).blur(); // 触发失去焦点事件
                    }
                });
                $(document).on("blur", "#demo_q", function () {
                    var query = $(this).val();
                    var tree = $('#channeltree').jstree(true);
                    
                    if (query.trim() !== '') {
                        // 执行搜索
                        tree.search(query);
                    } else {
                        // 如果查询为空，清除搜索结果
                        tree.clear_search();
                    }
                });
                $('#channeltree').on("changed.jstree", function (e, data) {
                    Config.module_id = data.selected.join(",");
                    console.log(Config.module_id);
                    $('#channeltree_content').jstree('refresh', Config.module_id);
                });
                $('#channeltree').jstree({
                    "themes": {
                        "stripes": false
                    },
                    "checkbox": {
                        "keep_selected_style": false,
                    },
                    "types": {
                        "course": {
                            "icon": "fa fa-folder-open",
                        },
                        "unit": {
                            "icon": "fa fa-th",
                        },
                        "lecture": {
                            "icon": "fa fa-list",
                        },
                        "link": {
                            "icon": "fa fa-link",
                        },
                        "disabled": {
                            "check_node": false,
                            "uncheck_node": false
                        }
                    },
                    'plugins': ["types","contextmenu"/*,"checkbox"*/,"search"],

                    "contextmenu":{
                        // keep_selected_style: false,
                        select_node:false,
                        show_at_node:false,
                        items: function(node) {
                            // 根据节点类型返回不同的菜单项
                            var items = {
                                "AddChild": {
                                    "separator_before": false,
                                    "separator_after": false,
                                    "_disabled": false,
                                    "label": "添加子级",
                                    "icon": "glyphicon glyphicon-plus",
                                    "action": function (data) {
                                        var inst = $.jstree.reference(data.reference),
                                            parentObj = inst.get_node(data.reference);
                                        
                                        // 创建临时节点，先不保存到数据库
                                        var newNode = {
                                            text: "新节点",
                                            type: "course",
                                            data: {
                                                parent_id: parentObj.id,
                                            }
                                        };
                                        
                                        inst.create_node(parentObj, newNode, "last", function (new_node) {
                                            // 创建节点后立即进入编辑状态，让用户输入名称
                                            setTimeout(function () { 
                                                inst.edit(new_node, null, function(node, status) {
                                                    // 用户完成编辑后，如果确认修改则保存到数据库
                                                    if (status) {
                                                        // 调用后台接口保存新节点
                                                        $.ajax({
                                                            url: 'system/org/addNode',
                                                            type: 'POST',
                                                            dataType: 'json',
                                                            data: {
                                                                parent_id: parentObj.id,
                                                                text: node.text,
                                                                type: node.type
                                                            },
                                                            success: function(response) {
                                                                if (response.code === 1) {
                                                                    // 保存成功，更新节点ID
                                                                    node.id = response.data.id;
                                                                    inst.set_id(node, response.data.id);
                                                                    Toastr.success("节点添加成功");
                                                                    // 使用表格API刷新
                                                                    $('#channeltree').jstree(true).refresh();
                                                                } else {
                                                                    Toastr.error(response.msg || "节点添加失败");
                                                                    // 添加失败，删除节点
                                                                    inst.delete_node(node);
                                                                }
                                                            },
                                                            error: function() {
                                                                Toastr.error("请求失败");
                                                                // 请求失败，删除节点
                                                                inst.delete_node(node);
                                                            }
                                                        });
                                                    } else {
                                                        // 用户取消编辑，删除临时节点
                                                        inst.delete_node(node);
                                                    }
                                                }); 
                                            }, 0);
                                        });
                                    }
                                }
                            };
                            
                            
                            // 如果不是根节点，添加修改按钮
                            if (node.parent !== "#") {
                                items["Modify"] = {
                                    "separator_before": false,
                                    "separator_after": false,
                                    "_disabled": false,
                                    "label": "修改",
                                    "icon": "glyphicon glyphicon-edit",
                                    "action": function (data) {
                                        var inst = $.jstree.reference(data.reference),
                                            node = inst.get_node(data.reference);
                                        
                                        // 保存修改前的原始文本
                                        var originalText = node.text;
                                        
                                        // 进入编辑状态
                                        inst.edit(node, null, function edited_callback(node, status) {
                                            // 用户完成编辑后执行
                                            if (status) {
                                                // 用户确认修改，调用接口更新节点
                                                $.ajax({
                                                    url: 'system/org/editNode',
                                                    type: 'POST',
                                                    dataType: 'json',
                                                    data: {
                                                        id: node.id,
                                                        text: node.text,
                                                        type: node.type
                                                    },
                                                    success: function(response) {
                                                        if (response.code === 1) {
                                                            Toastr.success("节点修改成功");
                                                        } else {
                                                            Toastr.error(response.msg || "节点修改失败");
                                                            // 修改失败，恢复原始文本
                                                            inst.rename_node(node, originalText);
                                                        }
                                                    },
                                                    error: function() {
                                                        Toastr.error("请求失败");
                                                        // 请求失败，恢复原始文本
                                                        inst.rename_node(node, originalText);
                                                    }
                                                });
                                            }
                                        });
                                    }
                                };
                            }
                            
                            return items;
                        }
                    },                    
                    "core": {
                        "multiple": true,
                        'check_callback': true, // 需要设置为true以允许创建节点
                        "data": {
                                'url' : tree_url,
                                // 'data' : function (node) {
                                //     return { 'id' : node };
                                // }
                            }
                        }
                });
            });
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
            
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));

                var customer_id = parent.$("#customer_id").val();
                var org_id = parent.$("#org_id").val();

                console.log('customer_id:', customer_id);
                console.log('org_id:', org_id);
                if (customer_id == '' || customer_id == null || customer_id<1) {
                    layer.alert("请在列表页选择所属客户后，再进行添加");
                    return false;
                }else{
                    $("#c-customer_id").val(customer_id);
                    $("#c-org_id").val(org_id);
                }

            }
        },
        select: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'user/user/select',
                    table: 'user',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'user.id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id'), sortable: true},
                        {field: 'group.name', title: __('Group')},
                        {field: 'username', title: __('Username'), operate: 'LIKE'},
                        {field: 'nickname', title: __('Nickname'), operate: 'LIKE'},
                        {field: 'mobile', title: __('Mobile'), operate: 'LIKE'},
                    ]
                ],queryParams: function (params) {
                    var extra = params.extra ? JSON.parse(params.extra) : {};
                    extra.module_id = Config.module_id;
                    params.extra = JSON.stringify(extra);
                    console.log(params);
                    return params;
                }
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
            
            // 批量确认选择
            $('.btn-confirm-choose').click(function () {

                var rows = $("#table").bootstrapTable('getSelections');
                var visibility = $("input[name='visibility']:checked").val();
                console.log('visibility：', visibility)
                console.log('select users', rows, rows.length, 'length')
                if (visibility == 'partial' && rows.length == 0){
                    layer.alert("请选择部分可见的用户");
                    return false;
                }

                const data = { 'visibility': visibility, 'users': rows }
                Fast.api.close(data);
            });
        }
    };
    return Controller;
});