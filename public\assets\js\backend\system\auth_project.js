define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'system/auth_project/index' + location.search,
                    add_url: 'system/auth_project/add',
                    edit_url: 'system/auth_project/edit',
                    del_url: 'system/auth_project/del',
                    multi_url: 'system/auth_project/multi',
                    import_url: 'system/auth_project/import',
                    table: 'auth_project',
                }
            });

            var table = $("#table");
            $(".btn-edit").data("area", ["90%", "90%"]);//编辑
            $(".btn-add").data("area", ["90%", "90%"]); //添加

            table.on('post-body.bs.table', function () {
                $(".btn-editone").data("area", ["90%", "90%"]);
                $("a.btn-product_config").attr('data-area', '["90%","90%"]');
                $("a.btn-authorization").attr('data-area', '["90%","90%"]');
            });

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'auth_id',
                sortName: 'auth_id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'auth_id', title: __('Auth_id')},
                        {field: 'auth_name', title: __('Auth_name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'status', title: __('Status'), searchList: {"normal":__('Normal'),"closed":__('Closed')}, formatter: Table.api.formatter.status},
                        {field: 'start_date', title: __('Start_date')},
                        {field: 'end_date', title: __('End_date')},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'customer.customer_name', title: __('Customer.customer_name'), operate: 'LIKE'},
                        {field: 'admin.nickname', title: __('Admin.nickname'), operate: 'LIKE'},
                        {
                            field: 'project_link',
                            title: __('项目链接'),
                            formatter: function (value, row, index) {
                                return '<a href="' + (row.project_url || 'javascript:;') + '" target="_blank" class="btn btn-xs btn-info">' + __('项目链接') + '</a>';
                            }
                        },
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate,buttons: [

                            {
                                name: 'product_config',
                                text: '产品配置',
                                title: '产品配置',
                                icon: 'fa fa-list',

                                classname: 'btn btn-xs btn-info btn-dialog btn-product_config',
                                url: function (row) {
                                    return 'system/auth_project/product_config?auth_id=' + row.auth_id
                                },
                            },

                            {
                                name: 'authorization',
                                text: '授权',
                                title: '授权',
                                icon: 'fa fa-list',
                                area: ['90%', '90%'],
                                classname: 'btn btn-xs btn-warning btn-dialog btn-authorization',
                                url: function (row) {
                                    return 'system/auth_project/authorization?auth_id=' + row.auth_id
                                },
                            },
                        ],}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            $("#project_config").click(function(){
                Fast.api.open('system/auth_project/product_config/auth_id/' + row.auth_id, '产品配置', {
                    area: ['90%', '90%'],
                    callback: (data) => {
                        console.log('outbound select callback', data)


                        // 排除已经存在的产品
                        for (let i = 0; i < this.outbounds.length; i++) {
                            for (let j = 0; j < data.length; j++) {
                                if (this.outbounds[i].product_id == data[j].product_id) {
                                    data.splice(j, 1)
                                }
                            }
                        }
                        this.outbounds = this.outbounds.concat(data)
                    }
                })
            })
        },
        recyclebin: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    'dragsort_url': ''
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: 'system/auth_project/recyclebin' + location.search,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {
                            field: 'deletetime',
                            title: __('Deletetime'),
                            operate: 'RANGE',
                            addclass: 'datetimerange',
                            formatter: Table.api.formatter.datetime
                        },
                        {
                            field: 'operate',
                            width: '140px',
                            title: __('Operate'),
                            table: table,
                            events: Table.api.events.operate,
                            buttons: [
                                {
                                    name: 'Restore',
                                    text: __('Restore'),
                                    classname: 'btn btn-xs btn-info btn-ajax btn-restoreit',
                                    icon: 'fa fa-rotate-left',
                                    url: 'system/auth_project/restore',
                                    refresh: true
                                },
                                {
                                    name: 'Destroy',
                                    text: __('Destroy'),
                                    classname: 'btn btn-xs btn-danger btn-ajax btn-destroyit',
                                    icon: 'fa fa-times',
                                    url: 'system/auth_project/destroy',
                                    refresh: true
                                }
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },

        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        product_config:() => {
            Form.api.bindevent($("form[role=form]"))
        },
        authorization: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'system/product/index' + location.search,
                    table: 'product',
                }
            });

            var table = $("#table");
            $(".btn-edit").data("area", ["90%", "90%"]);//编辑
            $(".btn-add").data("area", ["90%", "90%"]); //添加

            table.on('post-body.bs.table', function () {
                $(".btn-editone").data("area", ["90%", "90%"]);
            });
            
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"), function () {
                }, function () {
                }, function () {
                    $('#valid').click()
                    return valid_result;
                });

                $('#c-customer_id').on('change', (e) => {
                    const customerId = $("#c-customer_id").val();
                    if (customerId) {
                        // 发起 AJAX 请求获取客户详情
                        $.ajax({
                            url: 'system/customer/get_customer_manager',
                            method: 'GET',
                            data: { customer_id: customerId },
                            success: (res) => {
                                if (res.code === 1) {
                                    $('#c-customer_manager').val(res.data.manager);
                                    $('#c-phone').val(res.data.phone);
                                }
                            }
                        });
                    } else {
                        $('#c-customer_manager').val('');
                        $('#c-phone').val('');
                    }
                });
            }
        }
    };
    return Controller;
});
