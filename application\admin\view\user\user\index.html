<div class="row">

    <div col-md-3 hidden-xs hidden-sm>
        <div class="col-md-3 hidden-xs hidden-sm col-lg-2" id="channelbar" style="padding-right:0;">

            <div class="col-md-2 col-sm-4 col-xs-4" style="text-align:right;">
                <input type="text" value="" style="box-shadow:inset 0 0 4px #eee; width:200px; margin-top:10px; padding:6px 12px; border-radius:4px; border:1px solid silver; font-size:1.1em;" id="demo_q" placeholder="搜索">
            </div>

            <div class="panel panel-default panel-intro" style="margin-top: 60px;">
                <div class="panel-body">
                    <div id="channeltree">
                    </div>
                </div>
            </div>
        </div>      
    </div>

    <div class="col-xs-12 col-md-9">
        <div class="panel panel-default panel-intro">
            {:build_heading()}

            <div class="panel-body">
                <div id="myTabContent" class="tab-content">
                    <div class="tab-pane fade active in" id="one">
                        <div class="widget-body no-padding">
                            <div id="toolbar" class="toolbar">
                                {:build_toolbar('refresh,add,edit,del')}
                                <div class="dropdown btn-group {:$auth->check('user/user/multi')?'':'hide'}">
                                    <a class="btn btn-primary btn-more dropdown-toggle btn-disabled disabled" data-toggle="dropdown"><i class="fa fa-cog"></i> {:__('More')}</a>
                                    <ul class="dropdown-menu text-left" role="menu">
                                        <li><a class="btn btn-link btn-multi btn-disabled disabled" href="javascript:;" data-params="status=normal"><i class="fa fa-eye"></i> {:__('Set to normal')}</a></li>
                                        <li><a class="btn btn-link btn-multi btn-disabled disabled" href="javascript:;" data-params="status=hidden"><i class="fa fa-eye-slash"></i> {:__('Set to hidden')}</a></li>
                                    </ul>
                                </div>
                                
                                <a class="btn btn-info"  href="/excel/user.xlsx" target="_blank">下载导入模版</a>

                                {:build_toolbar('import')}

                                <a class="btn btn-info btn-classify dropdown-toggle btn-disabled disabled">批量迁移</a>

                            </div>
                            <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                                data-operate-edit="{:$auth->check('user/user/edit')}"
                                data-operate-del="{:$auth->check('user/user/del')}"
                                width="100%">
                            </table>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
<input type="hidden" id="customer_id" value="">
<input type="hidden" id="org_id" value="">

<script id="typetpl" type="text/html">
    <div class="col-md-3 hidden-xs hidden-sm">
        <div class="col-md-3 hidden-xs hidden-sm col-lg-2" id="channelbar" style="padding-right:0;">

            <div class="col-md-2 col-sm-4 col-xs-4" style="text-align:right;">
                <input type="text" value="" style="box-shadow:inset 0 0 4px #eee; width:200px; margin-top:10px; padding:6px 12px; border-radius:4px; border:1px solid silver; font-size:1.1em;" id="demo_q1" placeholder="搜索">
            </div>

            <div class="panel panel-default panel-intro" style="margin-top: 60px;">
                <div class="panel-body">
                    <div id="channeltree1">
                    </div>
                </div>
            </div>
        </div>      
    </div>
</script>