<!DOCTYPE HTML>
<html>
<head>
<meta charset="UTF-8">
<title>helper-test</title>
<script src="../dist/template-debug.js"></script>
</head>

<body>
<div id="content"></div>
<script id="test" type="text/html">
{{c || a || b}} {{c||a||b}}
{{time | dateFormat:'yyyy年 MM月 dd日 hh:mm:ss'}}
</script>

<script>
template.helper('dateFormat', function (date, format) {
    date = new Date(date);

    var map = {
        "M": date.getMonth() + 1, //月份 
        "d": date.getDate(), //日 
        "h": date.getHours(), //小时 
        "m": date.getMinutes(), //分 
        "s": date.getSeconds(), //秒 
        "q": Math.floor((date.getMonth() + 3) / 3), //季度 
        "S": date.getMilliseconds() //毫秒 
    };
    format = format.replace(/([yMdhmsqS])+/g, function(all, t){
        var v = map[t];
        if(v !== undefined){
            if(all.length > 1){
                v = '0' + v;
                v = v.substr(v.length-2);
            }
            return v;
        }
        else if(t === 'y'){
            return (date.getFullYear() + '').substr(4 - all.length);
        }
        return all;
    });
    return format;
});


var data = {
	a: 'aaa',
	b: 'bbb',
	time: 1408535976126
};
var html = template('test', data);
document.getElementById('content').innerHTML = html;
</script>
</body>
</html>