<?php

namespace app\admin\validate\system;

use think\Validate;

class AuthProjectData extends Validate
{
    /**
     * 验证规则
     */
    protected $rule = [
        'first_title' => 'require',
        'second_title' => 'require',
        'logo' => 'require',
        'school_name' => 'require',
        'course_title' => 'require',
        'course_tag' => 'require',
        'course_image' => 'require',
        'course_hours' => 'require',
        'course_description' => 'require',
        'module_one_name' => 'require',
        'module_two_name' => 'require',
        'module_three_name' => 'require',
        // 'module_one_en_name' => 'require',
        // 'module_two_en_name' => 'require',
        // 'module_three_en_name' => 'require',
    ];

    /**
     * 字段描述
     */
    protected $field = [
        'first_title' => '大标题',
        'second_title' => '小标题',
        'school_name' => '学校名称',
        'course_title' => '课程标题',
        'course_tag' => '课程标签',
        'course_image' => '课程封面',
        'course_hours' => '课程课时',
        'course_description' => '课程描述',
        'module_one_name' => '模块一名称',
        'module_two_name' => '模块二名称',
        'module_three_name' => '模块三名称',
        'module_one_en_name' => '模块一英文',
        'module_two_en_name' => '模块二英文',
        'module_three_en_name' => '模块三英文',

    ];
    /**
     * 提示消息
     */
    protected $message = [
    ];
    /**
     * 验证场景
     */
    protected $scene = [
        'add'  => [],
        'edit' => [],
    ];
    
}
