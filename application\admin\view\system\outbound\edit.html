<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Project_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-project_name" data-rule="required" class="form-control" name="row[project_name]" type="text" value="{$row.project_name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Category_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-category_id" min="0" data-rule="required" data-source="system/project_category/index" class="form-control selectpage" name="row[category_id]" type="text" value="{$row.category_id|htmlentities}" data-field="category_name" data-primary-key="category_id">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Customer_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-customer_id" min="0" data-rule="required" data-source="system/customer/index" class="form-control selectpage" name="row[customer_id]" type="text" value="{$row.customer_id|htmlentities}" data-field="customer_name" data-primary-key="customer_id">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Account_num')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-account_num" min="0" class="form-control" name="row[account_num]" type="number" value="{$row.account_num|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Quantity')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-quantity" min="0" class="form-control" name="row[quantity]" type="number" value="{$row.quantity|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Deploy_ids')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-deploy_ids" min="0" data-rule="required" data-source="system/deploy_method/index" data-multiple="true" class="form-control selectpage" name="row[deploy_ids]" type="text" value="{$row.deploy_ids|htmlentities}" data-field="deploy_name" data-primary-key="deploy_id">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Delivery_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-delivery_time" min="0" class="form-control datetimepicker" data-date-format="YYYY-MM-DD" data-use-current="true" name="row[delivery_time]" type="text" value="{:$row.delivery_time?datetime($row.delivery_time):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Company')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-company" class="form-control" name="row[company]" type="text" value="{$row.company|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Contract_file')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-contract_file" class="form-control" size="50" name="row[contract_file]" type="text" value="{$row.contract_file|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-contract_file" class="btn btn-danger faupload" data-input-id="c-contract_file" data-multiple="false" data-preview-id="p-contract_file"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-contract_file" class="btn btn-primary fachoose" data-input-id="c-contract_file" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-contract_file"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-contract_file"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Delivery_file')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-delivery_file" class="form-control" size="50" name="row[delivery_file]" type="text" value="{$row.delivery_file|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-delivery_file" class="btn btn-danger faupload" data-input-id="c-delivery_file" data-multiple="false" data-preview-id="p-delivery_file"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-delivery_file" class="btn btn-primary fachoose" data-input-id="c-delivery_file" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-delivery_file"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-delivery_file"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
