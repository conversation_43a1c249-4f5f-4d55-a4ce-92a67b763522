<div class="row">
    <div class="panel panel-default panel-intro">
        {:build_heading()}

        <div class="panel-body">
            <div id="myTabContent" class="tab-content">
                <div class="tab-pane fade active in" id="one">
                    <div class="widget-body no-padding">
                        <div id="toolbar" class="toolbar">
                            <!-- 添加状态选择单选按钮 -->
                            <div class="form-inline" style="margin-bottom: 10px;">
                                <div class="form-group">
                                    <label class="control-label">状态:</label>
                                    <div class="radio radio-inline">
                                        <label>
                                            <input type="radio" name="visibility" value="1" {if $visibility == 1}checked{/if}> 全部可见
                                        </label>
                                    </div>
                                    <div class="radio radio-inline">
                                        <label>
                                            <input type="radio" name="visibility" value="2" {if $visibility == 2}checked{/if}> 部分可见
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <a class="btn btn-info btn-confirm-choose">确定选择</a>
                        </div>
                        <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                            width="100%">
                        </table>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>
<style>
    .radio-inline {
        position: relative;
        display: inline-block;
        margin-right: 10px;
    }
    
    .radio-inline input[type="radio"] {
        margin-right: 5px;
    }
    
    .form-inline {
        margin-bottom: 10px;
    }
    
    .form-group {
        margin-bottom: 0;
    }
    </style>