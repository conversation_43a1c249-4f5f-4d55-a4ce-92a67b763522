﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    
    
    <link rel="apple-touch-icon" sizes="57x57" href="/apple-touch-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="114x114" href="/apple-touch-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="72x72" href="/apple-touch-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="144x144" href="/apple-touch-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="60x60" href="/apple-touch-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="120x120" href="/apple-touch-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="76x76" href="/apple-touch-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="152x152" href="/apple-touch-icon-152x152.png">
    <link rel="icon" type="image/png" href="/favicon-196x196.png" sizes="196x196">
    <link rel="icon" type="image/png" href="/favicon-160x160.png" sizes="160x160">
    <link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96">
    <link rel="icon" type="image/png" href="/favicon-16x16.png" sizes="16x16">
    <link rel="icon" type="image/png" href="/favicon-32x32.png" sizes="32x32">
    <meta name="msapplication-TileColor" content="#2b5797">
    <meta name="msapplication-TileImage" content="/mstile-144x144.png">

    <title></title>

    <link rel="stylesheet" type="text/css" media="screen"
          href="//maxcdn.bootstrapcdn.com/bootstrap/3.3.1/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="//maxcdn.bootstrapcdn.com/font-awesome/4.3.0/css/font-awesome.min.css">
    <link href="../css/prettify-1.0.css" rel="stylesheet">
    <link href="../css/base.css" rel="stylesheet">
    <link href="//cdn.rawgit.com/Eonasdan/bootstrap-datetimepicker/e8bddc60e73c1ec2475f827be36e1957af72e2ea/build/css/bootstrap-datetimepicker.css" rel="stylesheet">

    <script type="text/javascript" src="//code.jquery.com/jquery-2.1.1.min.js"></script>
    <script type="text/javascript" src="//maxcdn.bootstrapcdn.com/bootstrap/3.3.1/js/bootstrap.min.js"></script>
    
    <script src="//cdnjs.cloudflare.com/ajax/libs/moment.js/2.9.0/moment-with-locales.js"></script>
    
    
    <script src="//cdn.rawgit.com/Eonasdan/bootstrap-datetimepicker/e8bddc60e73c1ec2475f827be36e1957af72e2ea/src/js/bootstrap-datetimepicker.js"></script>
    
    
    
</head>

<body>

<div class="navbar navbar-default navbar-fixed-top" role="navigation">
    <div class="container">

        <!-- Collapsed navigation -->
        <div class="navbar-header">
            <!-- Expander button -->
            <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>

            <!-- Main title -->
            <a class="navbar-brand" href=""></a>
        </div>

        <!-- Expanded navigation -->
        <div class="navbar-collapse collapse">
            <!-- Main navigation -->
            <ul class="nav navbar-nav">
            
            
                <li >
                    <a href="..">Usage</a>
                </li>
            
            
            
                <li class="active">
                    <a href="./">Installing</a>
                </li>
            
            
            
                <li >
                    <a href="../Functions/">Functions</a>
                </li>
            
            
            
                <li >
                    <a href="../Options/">Options</a>
                </li>
            
            
            
                <li >
                    <a href="../Events/">Events</a>
                </li>
            
            
            
                <li >
                    <a href="../Changelog.md">Change Log</a>
                </li>
            
            
            
                <li >
                    <a href="../ContributorsGuide/">Dev Guide</a>
                </li>
            
            
            
                <li >
                    <a href="../Extras/">Extras</a>
                </li>
            
            
            
                <li >
                    <a href="../FAQ/">FAQs</a>
                </li>
            
            
            </ul>

            <!-- Search, Navigation and Repo links -->
            <ul class="nav navbar-nav navbar-right">
                
				
                
            </ul>
        </div>
    </div>
</div>

<div class="container">
    <div class="row">
        <div class="col-md-3"><script async type="text/javascript" src="//cdn.carbonads.com/carbon.js?serve=CK7DC5QN&placement=eonasdangithubio" id="_carbonads_js"></script>
<div class="bs-sidebar hidden-print affix well" role="complementary">    <ul class="nav bs-sidenav">
    
        <li class="main active"><a href="#minimal-requirements">Minimal Requirements</a></li>
        
    
        <li class="main "><a href="#installation-guides">Installation Guides</a></li>
        
            <li><a href="#bower">bower</a></li>
        
            <li><a href="#nuget">Nuget</a></li>
        
            <li><a href="#rails">Rails</a></li>
        
            <li><a href="#angular-wrapper">Angular Wrapper</a></li>
        
            <li><a href="#meteorjs">Meteor.js</a></li>
        
            <li><a href="#manual">Manual</a></li>
        
            <li><a href="#knockout">Knockout</a></li>
        
    
    </ul>
</div></div>
        <div class="col-md-8" role="main">
            <div class="alert alert-danger" style="font-size:1.5em;">
                <strong>Important!</strong>
                Please read this <a href="https://eonasdan.com/posts/state-of-my-picker" target="_blank">blog post</a>
            </div>
            

<h1 id="minimal-requirements">Minimal Requirements</h1>
<ol>
<li>jQuery</li>
<li>Moment.js</li>
<li>Bootstrap.js (transition and collapse are required if you're not using the full Bootstrap)</li>
<li>Bootstrap Datepicker script</li>
<li>Bootstrap CSS</li>
<li>Bootstrap Datepicker CSS</li>
<li>Locales: Moment's locale files are <a href="https://github.com/moment/moment/tree/master/locale">here</a></li>
</ol>
<h1 id="installation-guides">Installation Guides</h1>
<ul>
<li><a href="#bower-">Bower</a></li>
<li><a href="#nuget">Nuget</a></li>
<li><a href="#rails-">Rails</a></li>
<li><a href="#angular-wrapper">Angular</a></li>
<li><a href="#meteorjs">Meteor.js</a></li>
<li><a href="#manual">Manual</a></li>
</ul>
<h2 id="bower"><a href="http://bower.io">bower</a> <img alt="Bower version" src="https://badge.fury.io/bo/eonasdan-bootstrap-datetimepicker.png" /></h2>
<p>Run the following command:</p>
<pre><code>bower install eonasdan-bootstrap-datetimepicker#latest --save
</code></pre>

<p>Include necessary scripts and styles:</p>
<pre><code class="html">&lt;head&gt;
  &lt;!-- ... --&gt;
  &lt;script type=&quot;text/javascript&quot; src=&quot;/bower_components/jquery/jquery.min.js&quot;&gt;&lt;/script&gt;
  &lt;script type=&quot;text/javascript&quot; src=&quot;/bower_components/moment/min/moment.min.js&quot;&gt;&lt;/script&gt;
  &lt;script type=&quot;text/javascript&quot; src=&quot;/bower_components/bootstrap/dist/js/bootstrap.min.js&quot;&gt;&lt;/script&gt;
  &lt;script type=&quot;text/javascript&quot; src=&quot;/bower_components/eonasdan-bootstrap-datetimepicker/build/js/bootstrap-datetimepicker.min.js&quot;&gt;&lt;/script&gt;
  &lt;link rel=&quot;stylesheet&quot; href=&quot;/bower_components/bootstrap/dist/css/bootstrap.min.css&quot; /&gt;
  &lt;link rel=&quot;stylesheet&quot; href=&quot;/bower_components/eonasdan-bootstrap-datetimepicker/build/css/bootstrap-datetimepicker.min.css&quot; /&gt;
&lt;/head&gt;
</code></pre>

<h2 id="nuget">Nuget</h2>
<h3 id="less"><a href="https://www.nuget.org/packages/Bootstrap.v3.Datetimepicker/">LESS</a>: <img alt="NuGet version" src="https://badge.fury.io/nu/Bootstrap.v3.Datetimepicker.png" /></h3>
<pre><code>PM&gt; Install-Package Bootstrap.v3.Datetimepicker
</code></pre>

<h3 id="css"><a href="https://www.nuget.org/packages/Bootstrap.v3.Datetimepicker.CSS/">CSS</a>: <img alt="NuGet version" src="https://badge.fury.io/nu/Bootstrap.v3.Datetimepicker.CSS.png" /></h3>
<pre><code>PM&gt; Install-Package Bootstrap.v3.Datetimepicker.CSS
</code></pre>

<pre><code class="html">&lt;head&gt;
  &lt;script type=&quot;text/javascript&quot; src=&quot;/scripts/jquery.min.js&quot;&gt;&lt;/script&gt;
  &lt;script type=&quot;text/javascript&quot; src=&quot;/scripts/moment.min.js&quot;&gt;&lt;/script&gt;
  &lt;script type=&quot;text/javascript&quot; src=&quot;/scripts/bootstrap.min.js&quot;&gt;&lt;/script&gt;
  &lt;script type=&quot;text/javascript&quot; src=&quot;/scripts/bootstrap-datetimepicker.*js&quot;&gt;&lt;/script&gt;
  &lt;!-- include your less or built css files  --&gt;
  &lt;!-- 
  bootstrap-datetimepicker-build.less will pull in &quot;../bootstrap/variables.less&quot; and &quot;bootstrap-datetimepicker.less&quot;;
  or
  &lt;link rel=&quot;stylesheet&quot; href=&quot;/Content/bootstrap-datetimepicker.css&quot; /&gt;
  --&gt;
&lt;/head&gt;
</code></pre>

<h2 id="rails"><a href="http://rubygems.org/gems/bootstrap3-datetimepicker-rails">Rails</a> <img alt="Gem Version" src="https://badge.fury.io/rb/bootstrap3-datetimepicker-rails.png" /></h2>
<p>Add the following to your <code>Gemfile</code>:</p>
<pre><code class="ruby">gem 'momentjs-rails', '&gt;= 2.9.0'
gem 'bootstrap3-datetimepicker-rails', '~&gt; 4.14.30'
</code></pre>

<p>Note: You may need to change the version number above to the version number on the badge above.
Read the rest of the install instructions @ 
<a href="https://github.com/TrevorS/bootstrap3-datetimepicker-rails">TrevorS/bootstrap3-datetimepicker-rails</a></p>
<h2 id="angular-wrapper">Angular Wrapper</h2>
<p>Follow the link <a href="https://gist.github.com/eugenekgn/f00c4d764430642dca4b">here</a></p>
<h2 id="meteorjs">Meteor.js</h2>
<p>This widget has been package for the <a href="http://www.meteor.com/">Meteor.js</a> platform, to install it use meteorite as follows:</p>
<p><code>$ mrt add tsega:bootstrap3-datetimepicker</code></p>
<p>For more detail see the package page on <a href="http://atmospherejs.com/package/bootstrap3-datetimepicker">Atmosphere</a></p>
<h2 id="manual">Manual</h2>
<h3 id="acquire-jquery">Acquire <a href="http://jquery.com">jQuery</a></h3>
<h3 id="acquire-momentjs">Acquire  <a href="https://github.com/moment/moment">Moment.js</a></h3>
<h3 id="bootstrap-3-collapse-and-transition-plugins">Bootstrap 3 collapse and transition plugins</h3>
<p>Make sure to include *.JS files for plugins <a href="http://getbootstrap.com/javascript/#collapse">collapse</a> and <a href="http://getbootstrap.com/javascript/#transitions">transitions</a>. They are included with <a href="https://github.com/twbs/bootstrap/tree/master/js">bootstrap in js/ directory</a>
Alternatively you could include the whole bundle of bootstrap plugins from <a href="https://github.com/twbs/bootstrap/tree/master/dist/js">bootstrap.js</a></p>
<pre><code class="html">&lt;script type=&quot;text/javascript&quot; src=&quot;/path/to/jquery.js&quot;&gt;&lt;/script&gt;
&lt;script type=&quot;text/javascript&quot; src=&quot;/path/to/moment.js&quot;&gt;&lt;/script&gt;
&lt;script type=&quot;text/javascript&quot; src=&quot;/path/to/bootstrap/js/transition.js&quot;&gt;&lt;/script&gt;
&lt;script type=&quot;text/javascript&quot; src=&quot;/path/to/bootstrap/js/collapse.js&quot;&gt;&lt;/script&gt;
&lt;script type=&quot;text/javascript&quot; src=&quot;/path/to/bootstrap/dist/bootstrap.min.js&quot;&gt;&lt;/script&gt;
&lt;script type=&quot;text/javascript&quot; src=&quot;/path/to/bootstrap-datetimepicker.min.js&quot;&gt;&lt;/script&gt;
</code></pre>

<h2 id="knockout">Knockout</h2>
<pre><code>ko.bindingHandlers.dateTimePicker = {
    init: function (element, valueAccessor, allBindingsAccessor) {
        //initialize datepicker with some optional options
        var options = allBindingsAccessor().dateTimePickerOptions || {};
        $(element).datetimepicker(options);

        //when a user changes the date, update the view model
        ko.utils.registerEventHandler(element, &quot;dp.change&quot;, function (event) {
            var value = valueAccessor();
            if (ko.isObservable(value)) {
                if (event.date != null &amp;&amp; !(event.date instanceof Date)) {
                    value(event.date.toDate());
                } else {
                    value(event.date);
                }
            }
        });

        ko.utils.domNodeDisposal.addDisposeCallback(element, function () {
            var picker = $(element).data(&quot;DateTimePicker&quot;);
            if (picker) {
                picker.destroy();
            }
        });
    },
    update: function (element, valueAccessor, allBindings, viewModel, bindingContext) {

        var picker = $(element).data(&quot;DateTimePicker&quot;);
        //when the view model is updated, update the widget
        if (picker) {
            var koDate = ko.utils.unwrapObservable(valueAccessor());

            //in case return from server datetime i am get in this form for example /Date(93989393)/ then fomat this
            koDate = (typeof (koDate) !== 'object') ? new Date(parseFloat(koDate.replace(/[^0-9]/g, ''))) : koDate;

            picker.date(koDate);
        }
    }
};
</code></pre>

<h3 id="css-styles">CSS styles</h3>
<h4 id="using-less">Using LESS</h4>
<pre><code class="css">@import &quot;/path/to/bootstrap/less/variables&quot;;
@import &quot;/path/to/bootstrap-datetimepicker/src/less/bootstrap-datetimepicker-build.less&quot;;

// [...] your custom styles and variables
</code></pre>

<p>Using CSS (default color palette)</p>
<pre><code class="html">&lt;link rel=&quot;stylesheet&quot; href=&quot;/path/to/bootstrap-datetimepicker/build/css/bootstrap-datetimepicker.min.css&quot; /&gt;
</code></pre>
        </div>
    </div>
</div>



<script src="../js/prettify-1.0.min.js"></script>
<script src="../js/base.js"></script>
<script>
    if (top != self) {
        top.location.replace(self.location.href);
    }
    (function (i, s, o, g, r, a, m) {
        i['GoogleAnalyticsObject'] = r;
        i[r] = i[r] || function () {
            (i[r].q = i[r].q || []).push(arguments)
        }, i[r].l = 1 * new Date();
        a = s.createElement(o),
            m = s.getElementsByTagName(o)[0];
        a.async = 1;
        a.src = g;
        m.parentNode.insertBefore(a, m)
    })(window, document, 'script', '//www.google-analytics.com/analytics.js', 'ga');

    ga('create', 'UA-47462200-1', 'eonasdan.github.io');
    ga('send', 'pageview');
</script>
</body>
</html>