<?php

namespace app\admin\controller\system;

use app\common\controller\Backend;
use think\Db;
use think\exception\ValidateException;
use think\exception\PDOException;
use Exception;

/**
 * 组织管理
 *
 * @icon fa fa-circle-o
 */
class Org extends Backend
{

    /**
     * Org模型对象
     * @var \app\admin\model\system\Org
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\system\Org;

    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    ->with(['customer'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                $row->visible(['org_id','org_name','level','createtime','updatetime','parent_name']);
                $row->visible(['customer']);
				$row->getRelation('customer')->visible(['customer_name']);
                $row->parent_name = $this->model->where(['org_id' => $row->parent_id])->value('org_name');
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 树结构
     */

     public function listTree()
     {
         $treeList = [];
         $treeList[] = [
             'id'     => '0',
             'parent' => '#',
             'text'   => '全部',
             'type'   => 'lecture',
             'state'  => ['opened' => true]
         ];
 
         $customer = \app\admin\model\system\Customer::field('customer_id,customer_name')->select();
 
         foreach($customer as $key=>$val) {
             $treeList[] = [
                 'id'     => $val['customer_id'],
                 'parent' => '#',
                 'text'   => $val['customer_name'],
                 'type'   => 'lecture',
                 'state'  => ['opened' => false]
             ]; 
 
            $data = $this->model->where('customer_id',$val['customer_id'])
                        ->field('org_id, parent_id, org_name')
                        ->select();

             foreach($data as $k=>$v) {
                 $treeList[] = [
                     'id'     => $val['customer_id'] .'_'. $v['org_id'],
                     'parent' => $v['parent_id']?($val['customer_id'] .'_'. $v['parent_id']):($val['customer_id']),
                     'type'   => 'course',
                     'text'   => $v['org_name'],
                     'state'  => ['opened' => false]
                 ];
             }
         }
         return json($treeList);
     }

    /**
     * 添加节点
     */
    public function addNode(){
        
        $parent_id = $this->request->post('parent_id');
        $text = $this->request->post('text');
        if (empty($parent_id) || empty($text)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        
        $result = false;
        Db::startTrans();
        try {
            $params = [];
            $params['org_name'] = $text;
            if(strpos($parent_id,'_') !==false ){
                $arr = explode('_', $parent_id);
                $params['parent_id'] = $arr[count($arr)-1];
                $params['customer_id'] = $arr[0];
            }else{
                $params['customer_id'] = $parent_id;
                $params['parent_id'] = 0;
            }
            if(empty($params['customer_id'])){
                $this->error('请选择所属客户');
            }
            $result = $this->model->allowField(true)->save($params);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        $this->success();
    }

    /**
     * 修改节点
     */
    public function editNode()
    {
        $id = $this->request->post('id');
        $text = $this->request->post('text');
        if (empty($id) || empty($text)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        
        $result = false;
        Db::startTrans();
        try {
            $params = [];
            $params['org_name'] = $text;
            if(strpos($id,'_') !==false ){
                $arr = explode('_', $id);
                $org_id = $arr[count($arr)-1];
            }else{
                $this->error("参数错误");
            }
            $result = $this->model->allowField(true)->where('org_id', $org_id)->update($params);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        $this->success();
    }

    /**
     * 选择节点
     */
    public function selectNode(){
        $customer_id = $this->request->get('customer_id');
        $parent_id = $this->request->get('parent_id');
        if($parent_id === ''){
            return json(['code' => 1, 'count' => 0]);
        }
        if($customer_id){
            $count = $this->model->where(['customer_id'=>$customer_id,'parent_id'=>$parent_id])->count();
            return json(['code' => 1, 'count' => $count]);
        } else {
            return json(['code' => -1, 'msg' => '客户ID不能为空']);
        }
    }

}
