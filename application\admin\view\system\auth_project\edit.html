<script src="__CDN__/assets/js/backend/system/project/js/jquery.min.js"></script>
<script src="__CDN__/assets/js/backend/system/project/js/vue.js"></script>
<link rel="stylesheet" type="text/css" href="__CDN__/assets/js/backend/system/project/css/common.css"></link>
<script src="__CDN__/assets/js/backend/system/project/js/element-ui/index.js"></script>
<link rel="stylesheet" type="text/css" href="__CDN__/assets/js/backend/system/project/js/element-ui/index.css"></link>

<div id="app">
    <form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Auth_name')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-auth_name" data-rule="required" class="form-control" name="row[auth_name]" type="text" value="{$row.auth_name|htmlentities}">
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Customer_id')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-customer_id" min="0" data-rule="required" data-source="system/customer/index" class="form-control selectpage" name="row[customer_id]" type="text" value="{$row.customer_id|htmlentities}" data-field="customer_name" data-primary-key="customer_id">
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('客户负责人')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-customer_manager" class="form-control" name="row[customer_manager]" type="text" value="{$row.customer_manager|htmlentities}" readonly>
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('客户负责人手机号')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-phone" class="form-control" name="row[phone]" type="text" value="{$row.phone|htmlentities}" readonly>
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
            <div class="col-xs-12 col-sm-8">
                
                <div class="radio">
                {foreach name="statusList" item="vo"}
                <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
                {/foreach}
                </div>

            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Start_date')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-start_date" class="form-control datetimepicker" data-date-format="YYYY-MM-DD" data-use-current="true" name="row[start_date]" type="text" value="{$row.start_date|htmlentities}">
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('End_date')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-end_date" class="form-control datetimepicker" data-date-format="YYYY-MM-DD" data-use-current="true" name="row[end_date]" type="text" value="{$row.end_date|htmlentities}">
            </div>
        </div>

        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('出库列表')}:</label>
            <div class="col-xs-12 col-sm-8">
                <a href="javascript:;" class="btn btn-sm btn-success btn-fix-configs margin-bottom" @click="openOutboundSelect()"><i class="fa fa-cog"></i> 选择出库列表</a>

                {include file="../application/admin/view/system/auth_project/outbound_setting.html" /}
            </div>

        </div>

        <div class="form-group layer-footer">
            <label class="control-label col-xs-12 col-sm-2"></label>
            <div class="col-xs-12 col-sm-8">
                <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            </div>
        </div>
    </form>

</div>

<!--产品扩展-->
{include file="../application/admin/view/system/auth_project/extend.html" /}