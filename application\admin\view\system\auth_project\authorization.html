<script src="__CDN__/assets/js/backend/system/project/js/jquery.min.js"></script>
<script src="__CDN__/assets/js/backend/system/project/js/vue.js"></script>
<link rel="stylesheet" type="text/css" href="__CDN__/assets/js/backend/system/project/css/common.css"></link>
<script src="__CDN__/assets/js/backend/system/project/js/element-ui/index.js"></script>
<link rel="stylesheet" type="text/css" href="__CDN__/assets/js/backend/system/project/js/element-ui/index.css"></link>
<div id="app">
    <div class="panel panel-default panel-intro">
        <div class="panel-body">
            <div id="myTabContent" class="tab-content">
                <div class="tab-pane fade active in" id="one">
                    <div class="widget-body no-padding">
                        <!--列表-->
                        <el-table :data="product" border style="width: 100%">
                            <el-table-column prop="product_id" label="产品ID" width="80"></el-table-column>
                            <el-table-column prop="product_name" label="产品名称"></el-table-column>
                            <el-table-column prop="platform_name" label="平台名称"></el-table-column>
                            <el-table-column prop="visible_range" label="可见范围"></el-table-column>
                            <el-table-column fixed="right" label="操作" width="150">
                                <template slot-scope="scope">
                                    <el-button type="primary" icon="el-icon-edit" size="small" @click="setVisibleRange(scope.row)">设置</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed" @click="submit()">{:__('OK')}</button>
        </div>
    </div>
</div>
<script>
    {notempty name="product"}
        var product = {:json_encode($product, 256)};
    {else/}
        var product = {};
    {/notempty}
    var app = new Vue({
        el: '#app',
        data: {
            product: [],
            users:{}
        },
        created() {
            // 编辑时初始化数据
            if (product) {
                let products = product ? product : []
                this.product = products
                console.log('product', this.product)
            }
        },
        mounted() {
            // 绑定提交按钮事件
            $('#submit-btn').on('click', function() {
                // 触发验证按钮
                $('#valid').click();
                
                // 检查验证结果
                if (valid_result) {
                    // 验证通过，提交表单
                    $('#add-form, #edit-form').submit();
                }
            });
        },
        methods: {
            // 设置可见范围
            setVisibleRange(row) {
                console.log('openSetVisibleRange')

                console.log("product_id：", row.product_id)
                console.log("visibility：", row.visibility)

                Fast.api.open('user/user/select?module_id=' + row.customer_id + '&visibility=' + row.visibility, '设置可见范围', {
                    area: ['90%', '90%'],
                    callback: (data) => {
                        let userIds = [];
                        if (data.users && Array.isArray(data.users)) {
                            userIds = data.users.map(user => user.id).filter(id => id !== undefined);
                        }
                        this.users[row.product_id] = { 
                            'visibility': data.visibility, 
                            'users': userIds 
                        };
                        console.log("this.users:", this.users);
                    }
                })
            },
            // 提交
            submit(index) {
                console.log("submit")
                Fast.api.ajax({
                    url: 'system/auth_project/authorization',
                    data: {users: this.users, auth_id: '{$auth_id}'},
                    success: function (res) {
                        Layer.alert(res.msg);
                        if (res.code == 1) {
                            setTimeout(() => {
                                parent.location.reload();
                            }, 1000);
                        }
                    }
                });
            }
        }
    })
</script>