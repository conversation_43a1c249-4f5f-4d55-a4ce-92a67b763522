﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    
    
    <link rel="apple-touch-icon" sizes="57x57" href="/apple-touch-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="114x114" href="/apple-touch-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="72x72" href="/apple-touch-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="144x144" href="/apple-touch-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="60x60" href="/apple-touch-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="120x120" href="/apple-touch-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="76x76" href="/apple-touch-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="152x152" href="/apple-touch-icon-152x152.png">
    <link rel="icon" type="image/png" href="/favicon-196x196.png" sizes="196x196">
    <link rel="icon" type="image/png" href="/favicon-160x160.png" sizes="160x160">
    <link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96">
    <link rel="icon" type="image/png" href="/favicon-16x16.png" sizes="16x16">
    <link rel="icon" type="image/png" href="/favicon-32x32.png" sizes="32x32">
    <meta name="msapplication-TileColor" content="#2b5797">
    <meta name="msapplication-TileImage" content="/mstile-144x144.png">

    <title></title>

    <link rel="stylesheet" type="text/css" media="screen"
          href="//maxcdn.bootstrapcdn.com/bootstrap/3.3.1/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="//maxcdn.bootstrapcdn.com/font-awesome/4.3.0/css/font-awesome.min.css">
    <link href="../css/prettify-1.0.css" rel="stylesheet">
    <link href="../css/base.css" rel="stylesheet">
    <link href="//cdn.rawgit.com/Eonasdan/bootstrap-datetimepicker/e8bddc60e73c1ec2475f827be36e1957af72e2ea/build/css/bootstrap-datetimepicker.css" rel="stylesheet">

    <script type="text/javascript" src="//code.jquery.com/jquery-2.1.1.min.js"></script>
    <script type="text/javascript" src="//maxcdn.bootstrapcdn.com/bootstrap/3.3.1/js/bootstrap.min.js"></script>
    
    <script src="//cdnjs.cloudflare.com/ajax/libs/moment.js/2.9.0/moment-with-locales.js"></script>
    
    
    <script src="//cdn.rawgit.com/Eonasdan/bootstrap-datetimepicker/e8bddc60e73c1ec2475f827be36e1957af72e2ea/src/js/bootstrap-datetimepicker.js"></script>
    
    
    
</head>

<body>

<div class="navbar navbar-default navbar-fixed-top" role="navigation">
    <div class="container">

        <!-- Collapsed navigation -->
        <div class="navbar-header">
            <!-- Expander button -->
            <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>

            <!-- Main title -->
            <a class="navbar-brand" href=""></a>
        </div>

        <!-- Expanded navigation -->
        <div class="navbar-collapse collapse">
            <!-- Main navigation -->
            <ul class="nav navbar-nav">
            
            
                <li >
                    <a href="..">Usage</a>
                </li>
            
            
            
                <li >
                    <a href="../Installing/">Installing</a>
                </li>
            
            
            
                <li >
                    <a href="../Functions/">Functions</a>
                </li>
            
            
            
                <li >
                    <a href="../Options/">Options</a>
                </li>
            
            
            
                <li >
                    <a href="../Events/">Events</a>
                </li>
            
            
            
                <li >
                    <a href="../Changelog.md">Change Log</a>
                </li>
            
            
            
                <li >
                    <a href="../ContributorsGuide/">Dev Guide</a>
                </li>
            
            
            
                <li >
                    <a href="../Extras/">Extras</a>
                </li>
            
            
            
                <li >
                    <a href="../FAQ/">FAQs</a>
                </li>
            
            
            </ul>

            <!-- Search, Navigation and Repo links -->
            <ul class="nav navbar-nav navbar-right">
                
				
                
            </ul>
        </div>
    </div>
</div>

<div class="container">
    <div class="row">
        <div class="col-md-3"><script async type="text/javascript" src="//cdn.carbonads.com/carbon.js?serve=CK7DC5QN&placement=eonasdangithubio" id="_carbonads_js"></script>
<div class="bs-sidebar hidden-print affix well" role="complementary">    <ul class="nav bs-sidenav">
    
        <li class="main active"><a href="#version-4">Version 4</a></li>
        
            <li><a href="#41742">4.17.42</a></li>
        
            <li><a href="#41737">4.17.37</a></li>
        
            <li><a href="#41535">4.15.35</a></li>
        
            <li><a href="#41430">4.14.30</a></li>
        
            <li><a href="#4714">4.7.14</a></li>
        
            <li><a href="#400">4.0.0</a></li>
        
    
        <li class="main "><a href="#version-3">Version 3</a></li>
        
            <li><a href="#300">3.0.0</a></li>
        
    
        <li class="main "><a href="#version-2">Version 2</a></li>
        
            <li><a href="#2132-hotfix">2.1.32 (Hotfix)</a></li>
        
            <li><a href="#2130">2.1.30</a></li>
        
            <li><a href="#2120">2.1.20</a></li>
        
            <li><a href="#2111">2.1.11</a></li>
        
            <li><a href="#215">2.1.5</a></li>
        
            <li><a href="#201">2.0.1</a></li>
        
            <li><a href="#200">2.0.0</a></li>
        
    
    </ul>
</div></div>
        <div class="col-md-8" role="main">
            <div class="alert alert-danger" style="font-size:1.5em;">
                <strong>Important!</strong>
                Please read this <a href="https://eonasdan.com/state-of-my-picker" target="_blank">blog post</a>
            </div>
            

<h1 id="version-4">Version 4</h1>
<h2 id="41742">4.17.42</h2>
<h3 id="bug-squashing">Bug Squashing</h3>
<ul>
<li>fixed moment dependencies to all be the same</li>
<li>defaulted <code>option.timeZone</code> to <code>''</code> instead of UTC. This way it will default to the local timezone if it's not set.</li>
<li>fixed #959</li>
<li>fixed #1311 internal <code>getMoment</code> function no longer sets <code>startOf('d')</code></li>
<li>fixed #935</li>
</ul>
<h3 id="other">Other</h3>
<ul>
<li>moved some (will move the rest soon) inline docs to JSDoc now that ReSharper supports it.</li>
<li>moved getter/setter functions to options page instead. #1313</li>
</ul>
<h2 id="41737">4.17.37</h2>
<h3 id="new-features">New Features</h3>
<ul>
<li>Momentjs TZ intergration #1242 thanks @bodrick</li>
<li>Independent CSS file, in case you don't want bootstrap for some reason</li>
</ul>
<h3 id="bug-squashing_1">Bug Squashing</h3>
<ul>
<li>Slight changes decade view</li>
<li>Moved all tooltip text to <code>tooltips</code></li>
<li>fixed #1212</li>
</ul>
<h2 id="41535">4.15.35</h2>
<h3 id="new-features_1">New Features</h3>
<p><code>tooltips</code> allows custom, localized text to be included for icon tooltips</p>
<h3 id="bug-squashing_2">Bug Squashing</h3>
<p>fixed #1066</p>
<p>fixed #1087 <code>sideBySide</code> properly supports <code>toolbarPlacement [top, bottom]</code></p>
<p>fixed #1119 </p>
<p>fixed #1069 added input.blur()</p>
<p>fixed #1049 fixed doc example </p>
<p>fixed #999 picker now looks for an element with <code>.input-group-addon</code></p>
<h2 id="41430">4.14.30</h2>
<h3 id="new-features_2">New Features</h3>
<p><code>disabledTimeIntervals</code> #644</p>
<p><code>allowInputToggle</code> #929</p>
<p><code>focusOnShow</code> #884</p>
<p>public <code>viewDate</code> function #872</p>
<p><code>enabledHours</code> and <code>disabledHours</code>.</p>
<p><code>dp.update</code> fires when <code>viewDate</code> is changed (in most cases) #937</p>
<p><code>viewMode</code> now supports a decades view. </p>
<p><strong>Note</strong>: because the year picker shows 12 years at a time, I've elected to make this view show blocks of 12 years</p>
<p><strong>Note</strong>: when selecting a decade the <code>viewDate</code> will change to the <strong>center</strong> of the selected years</p>
<p><code>parseInputDate</code> #1095</p>
<h3 id="bug-squashing_3">Bug Squashing</h3>
<p>fixed #815 by adding <code>.wider</code> when using both seconds and am/pm.</p>
<p>fixed #816 changed both min/max date to move the selected date inside.</p>
<p>fixed #855 #881 <code>fillDate</code>, <code>fillMonths</code>, <code>fillDow</code> uses <code>startOf('day')</code>, which will hopefully fix the DST issues.</p>
<p>fixed #885 <code>daysOfWeekDisabled</code> will move the date to a valid date if <code>useCurrent</code> is <code>true</code>. Today button will check if the DoW is disabled.</p>
<p>fixed #906</p>
<p>fixed #912 if <code>useCurrent:false</code> month and year view will no longer have the current month/year selected.</p>
<p>fixed #914 <code>use24hours</code> will ignore anything in side of <code>[]</code> in the format string.</p>
<p>fixed #916 added titles to all icons. At some point the text should be moved to the icon's array, but this would probably be a breaking change.</p>
<p>fixed #940 added -1 tab index to am/pm selector</p>
<h3 id="other-changes">Other Changes</h3>
<p>changed in/decrement behavior to check if the new date is valid at that granularity (hours, minutes, seconds). will also validate as before</p>
<h2 id="4714">4.7.14</h2>
<p>Added several in new features:</p>
<pre><code>`keybinds`, `inline`, `debug`, `clear()`, `showClose`, `ingoreReadOnly`, `datepickerInput` and `keepInvalid`.
</code></pre>
<p>Bug squashing</p>
<h2 id="400">4.0.0</h2>
<h4 id="changes-for-using-the-component">Changes for using the component</h4>
<ul>
<li>
<p>Defined a <a href="https://github.com/Eonasdan/bootstrap-datetimepicker/wiki/Version-4-Public-API">Public API</a> and hidden rest of functions, variables so that all configuration options can be changed dynamically.</p>
</li>
<li>
<p><code>set/getDate()</code> is now replaced with an overloaded <code>date()</code> function. Use it without a parameter to get the currently set date or with a parameter to set the date.</p>
</li>
<li>
<p><code>hide()</code>, <code>show()</code>, <code>toggle()</code>, <code>enable()</code>, <code>disable()</code> and the rest of setter functions now support chaining. ie <code>$('#id').data('DateTimePicker').format('DD-MM-YYYY').minDate(moment()).defaultDate(moment()).show()</code> works</p>
</li>
<li>
<p>Replaced previous - next buttons in Date subviews with configurable icons</p>
</li>
<li>
<p>Changed <code>language</code> option name to <code>locale</code> to be inline with moment naming</p>
</li>
<li>
<p>Implemented #402 all data-date-* variables are more readable and also match with the ones in the configuration object</p>
</li>
<li>
<p><code>options.direction</code> and <code>options.orientation</code> were merged into a single object <code>options.widgetPositioning</code> with <code>vertical</code> and <code>horizontal</code> keys that take a string value of <code>'auto', 'top', 'bottom'</code> and <code>'auto', 'left', 'right'</code> respectively. Note that the <code>'up'</code> option was renamed to <code>'top'</code></p>
</li>
</ul>
<h4 id="added-functionality">Added functionality</h4>
<ul>
<li>
<p>added a second way to define options as data attributes. Instead of adding distinct <code>data-date-*</code> config options you can now also pass a <code>data-date-options</code> attribute containing an object just the same as the options object that <code>element.datetimepicker</code> constructor call takes</p>
</li>
<li>
<p>also added a <code>options()</code> public api function to get/set that takes an option object and applies it to the component in one call</p>
</li>
<li>
<p>Implemented <a href="https://github.com/Eonasdan/bootstrap-datetimepicker/issues/130">#130</a> by introducing a boolean <code>options.calendarWeeks</code> and <code>calendarWeeks()</code> api function</p>
</li>
<li>
<p>Implemented <a href="https://github.com/Eonasdan/bootstrap-datetimepicker/issues/328">#328</a>, <a href="https://github.com/Eonasdan/bootstrap-datetimepicker/issues/426">#426</a></p>
</li>
<li>
<p>Implemented <a href="https://github.com/Eonasdan/bootstrap-datetimepicker/issues/432">#432</a>. Widget DOM element is now lazily added only when shown and removed from the document when hidden.</p>
</li>
<li>
<p>Implemented <a href="https://github.com/Eonasdan/bootstrap-datetimepicker/issues/141">#141</a> and <a href="https://github.com/Eonasdan/bootstrap-datetimepicker/issues/283">#283</a></p>
</li>
</ul>
<h4 id="contributors-related-internal-code-changes">Contributors related internal code changes</h4>
<ul>
<li>
<p>Refactor all UI click functions and put them as functions in the actions array private variable</p>
</li>
<li>
<p>Refactor template building process to seperate functions according to what they do</p>
</li>
<li>
<p>Remove some styles that where hardcoded in the javascript code</p>
</li>
<li>
<p>Refactor all code that changes the picker.date to change it through the setValue function to allow one place for validation logic (min/max/weekdaysenabled etc) and also one place for emmiting dp.change events</p>
</li>
<li>
<p>The v4beta branch code includes all fixes up to v.3.1.2</p>
</li>
<li>
<p>Added <code>toggle()</code> to the public API which toggles the visibility of the DateTimePicker</p>
</li>
<li>
<p>Refactored set function to be included in the setValue function</p>
</li>
<li>
<p>Added a testing framework using jasmine and phantom.js</p>
</li>
</ul>
<h1 id="version-3">Version 3</h1>
<h2 id="300">3.0.0</h2>
<ul>
<li>Fix for #170, #179, #183: Changed event to <code>dp.-</code>. This should fix the double change event firing.</li>
<li>Fix for #192: <code>setDate</code> now fires <code>dp.change</code></li>
<li>Fix for #182: Picker will <strong>not</strong> set the default date if the input field has a value</li>
<li>Fix for #169: Seconds doesn't get reset when changing the date (Thanks to PR #174)</li>
<li>Fix for #168 z-index fix for BS modal</li>
<li>Fix for #155 Picker properly displays the active year and month</li>
<li>Fix for #154 CSS update to fix the collapse jump</li>
<li>Fix for #150 and #75 <code>minViewMode</code> and <code>viewMode</code> work properly</li>
<li>Fix for #147 AM/PM won't toggle when selecting a value from the hours grid</li>
<li>Fix for #44 Finally! It's here!! Thanks to @ruiwei and his code on #210 picker will adjust the positioning of the widget.</li>
</ul>
<h4 id="manually-merged-pr">Manually merged PR</h4>
<ul>
<li>PR #178 When using <code>minuteStepping</code> the minute select grid will only show available steppings</li>
<li>PR #195, #197 Using the <code>data-OPTION</code> has been changed to <code>data-date-OPTION</code>. These options are expected to be on the <code>input-group</code> if you're using the <code>input-group</code> <strong>or</strong> the a bare input field if you're not using the <code>input-group</code></li>
<li>PR #184 The option <code>sideBySide</code> change be used to display both the d and the timepicker side by side</li>
<li>PR #143 Added option <code>daysOfWeekDisabled: []</code>. For example, use <code>daysOfWeekDisabled: [0,6]</code> to disable Sunday and Saturday</li>
</ul>
<h4 id="other-changes_1"><strong>Other Changes</strong></h4>
<ul>
<li>Changed picker width to 300px if using seconds and am/pm</li>
<li>Added option <code>useCurrent</code>, thanks to @ruiwei. When true, picker will set the value to the current date/time (respects picker's format)</li>
<li>Added option <code>showToday</code>, thanks to @ruiwei. When true, picker will display a small arrow to indicate today's date.</li>
<li>Changed <code>startDate</code> to <code>minDate</code> and <code>endDate</code> to <code>maxDate</code> to make it more clear what these options do.</li>
</ul>
<h1 id="version-2">Version 2</h1>
<h4 id="2132-hotfix">2.1.32 (Hotfix)</h4>
<ul>
<li>Fix for #151: When a bad date value or the picker is cleared, the plugin will not longer attempt to reset it back to the previous date</li>
<li>Fix for #140: <code>setDate</code> can be given <code>null</code> to force clear the picker</li>
</ul>
<h4 id="2130">2.1.30</h4>
<h5 id="important-buildless-file-name-has-been-been-changed-to-bootstrap-datetimepicker-buildless-to-prevent-collisions">Important! <code>build.less</code> file name has been been changed to <code>bootstrap-datetimepicker-build.less</code> to prevent collisions</h5>
<ul>
<li>Fix for #135: <code>setStartDate</code> and <code>setEndDate</code> should now properly set.</li>
<li>Fix for #133: Typed in date now respects en/disabled dates</li>
<li>Fix for #132: En/disable picker function works again</li>
<li>Fix for #117, #119, #128, #121: double event <code>change</code> event issues should be fixed</li>
<li>
<p>Fix for #112: <code>change</code> function no longer sets the input to a blank value if the passed in date is invalid</p>
</li>
<li>
<p>Enhancement for #103: Increated the <code>z-index</code> of the widget</p>
</li>
</ul>
<h4 id="2120">2.1.20</h4>
<ul>
<li>Fix for #83: Changes to the picker should fire native <code>change</code> event for knockout and the like as well as <code>change.dp</code> which contains the old date and the new date</li>
<li>Fix for #78: Script has been update for breaking changes in Moment 2.4.0</li>
<li>
<p>Fix for #73: IE8 should be working now</p>
</li>
<li>
<p>Enhancement for #79: <code>minuteStepping</code> option takes a number (default is 1). Changing the minutes in the time picker will step by this number.</p>
</li>
<li>Enhancement for #74 and #65: <code>useMinutes</code> and <code>useSeconds</code> are now options. Disabling seconds will hide the seconds spinner. Disabling minutes will display <code>00</code> and hide the arrows</li>
<li>Enhancement for #67: Picker will now attempt to convert all <code>data-OPTION</code> into its appropriate option</li>
</ul>
<h4 id="2111">2.1.11</h4>
<ul>
<li>Fix for #51, #60</li>
<li>Fix for #52: Picker has its own <code>moment</code> object since moment 2.4.0 has removed global reference</li>
<li>Fix for #57: New option for <code>useStrict</code>. When validating dates in <code>update</code> and <code>change</code>, the picker can use a stricter formatting validation</li>
<li>Fix for #61: Picker should now properly take formatted date. Should also have correct start of the week for locales.</li>
<li>Fix for #62: Default format will properly validate time picker only.</li>
</ul>
<h4 id="215">2.1.5</h4>
<ul>
<li>Custom icons, such as Font Awesome, are now supported. (#49)</li>
<li>If more then one <code>input-group-addon</code> is present use <code>datepickerbutton</code> to identify where the picker should popup from. (#48)</li>
<li>New Event: <code>error.dp</code>. Fires when Moment cannot parse the date or when the timepicker cannot change because of a <code>disabledDates</code> setting. Returns a Moment date object. The specific error can be found be using <code>invalidAt()</code>. For more information see <a href="http://momentjs.com/docs/#/parsing/is-valid/">Moment's docs</a></li>
<li>Fix for #42, plugin will now check for <code>A</code> or <code>a</code> in the format string to determine if the AM/PM selector should display.</li>
<li>Fix for #45, fixed null/empty and invalid dates</li>
<li>Fix for #46, fixed active date highlighting</li>
<li>Fix for #47, <code>change.dp</code> event to also include the previous date.</li>
</ul>
<h4 id="201">2.0.1</h4>
<ul>
<li>New event <code>error.dp</code> fires when plugin cannot parse date or when increase/descreasing hours/minutes to a disabled date.</li>
<li>Minor fixes</li>
</ul>
<h4 id="200">2.0.0</h4>
<ul>
<li><code>disabledDates</code> is now an option to set the disabled dates. It accepts date objects like <code>new Date("November 12, 2013 00:00:00")</code> and <code>12/25/2013' and</code>moment` date objects</li>
<li>Events are easier to use</li>
</ul>
        </div>
    </div>
</div>



<script src="../js/prettify-1.0.min.js"></script>
<script src="../js/base.js"></script>
<script>
    if (top != self) {
        top.location.replace(self.location.href);
    }
    (function (i, s, o, g, r, a, m) {
        i['GoogleAnalyticsObject'] = r;
        i[r] = i[r] || function () {
            (i[r].q = i[r].q || []).push(arguments)
        }, i[r].l = 1 * new Date();
        a = s.createElement(o),
            m = s.getElementsByTagName(o)[0];
        a.async = 1;
        a.src = g;
        m.parentNode.insertBefore(a, m)
    })(window, document, 'script', '//www.google-analytics.com/analytics.js', 'ga');

    ga('create', 'UA-47462200-1', 'eonasdan.github.io');
    ga('send', 'pageview');
</script>
</body>
</html>