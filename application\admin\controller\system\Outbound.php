<?php

namespace app\admin\controller\system;

use app\common\controller\Backend;
use think\Db;
use think\exception\ValidateException;
use think\exception\PDOException;
use Exception;

/**
 * 出库管理
 *
 * @icon fa fa-circle-o
 */
class Outbound extends Backend
{
    protected $dataLimit = true;

    /**
     * Outbound模型对象
     * @var \app\admin\model\system\Outbound
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\system\Outbound;
        $this->view->assign("statusList", $this->model->getStatusList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    ->with(['category','customer','method'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                $row->visible(['outbound_id','project_name','account_num','quantity','delivery_time','company','status']);
                $row->visible(['category']);
				$row->getRelation('category')->visible(['category_name']);
				$row->visible(['customer']);
				$row->getRelation('customer')->visible(['customer_name']);
				$row->visible(['method']);
				$row->getRelation('method')->visible(['deploy_name']);
                $row->status = 1;
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    

    /**
     * 添加
     *
     * @return string
     * @throws \think\Exception
     */
    public function add()
    {
        if (false === $this->request->isPost()) {
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);

        if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
            $params[$this->dataLimitField] = $this->auth->id;
        }
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                $this->model->validateFailException()->validate($validate);
            }
            $result = $this->model->allowField(true)->save($params);
            $outbound_id = $this->model->outbound_id;
            // 出货产品关联入库
            if($result){
                $productArr = json_decode($params['products'], true);
                $insert = [];
                foreach ($productArr as $val) {
                    $insert[] = [
                        'outbound_id' => $outbound_id,
                        'product_id' => $val['id'],
                        'type' => $val['type']
                    ];
                }
                Db::name('outbound_product')->insertAll($insert);
            }

            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        $this->success();
    }

    /**
     * 编辑
     *
     * @param $ids
     * @return string
     * @throws DbException
     * @throws \think\Exception
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }
        if (false === $this->request->isPost()) {
            $row['customer_manager'] = \app\admin\model\system\Customer::where('customer_id', $row['customer_id'])->value('customer_owner');
            $productsList = Db::name('outbound_product')->where('outbound_id', $row['outbound_id'])->select();
            $productsArr = [];
            foreach ($productsList as $product) {
                $productRow = \app\admin\model\system\Product::where('product_id', $product['product_id'])->find();
                $platform_name = \app\admin\model\system\Platform::where('id', $productRow['platform_id'])->value('name');
                $productsArr[] = [
                    'product_id' => $product['product_id'],
                    'product_name' => $productRow['product_name'],
                    'platform' => ['name' => $platform_name],
                    'type' => $product['type']
                ];
            }
            $row['products'] = $productsArr;
            $this->view->assign('row', $row);
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                $row->validateFailException()->validate($validate);
            }
            $result = $row->allowField(true)->save($params);
            // 出货产品关联入库
            if($result){
                // 删除原有出货产品记录
                Db::name('outbound_product')->where('outbound_id', $ids)->delete();
                // 插入新的出货产品记录
                $productArr = json_decode($params['products'], true);
                $insert = [];
                foreach ($productArr as $val) {
                    $insert[] = [
                        'outbound_id' => $ids,
                        'product_id' => $val['id'],
                        'type' => $val['type']
                    ];
                }
                Db::name('outbound_product')->insertAll($insert);
            }
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success();
    }

    /**
     * 选择出货页面
     */
    public function select()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    ->with(['category','customer','method'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                $row->visible(['outbound_id','project_name','account_num','quantity','delivery_time','company','status']);
                $row->visible(['category']);
				$row->getRelation('category')->visible(['category_name']);
				$row->visible(['customer']);
				$row->getRelation('customer')->visible(['customer_name']);
				$row->visible(['method']);
				$row->getRelation('method')->visible(['deploy_name']);
                $row->status = 1;
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

}
