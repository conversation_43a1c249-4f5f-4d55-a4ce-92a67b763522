<?php

namespace app\admin\controller\system;

use app\common\controller\Backend;
use think\Db;
use think\exception\ValidateException;
use think\exception\PDOException;
use Exception;

/**
 * 授权项目管理
 *
 * @icon fa fa-circle-o
 */
class AuthProject extends Backend
{
    protected $dataLimit = true;

    /**
     * AuthProject模型对象
     * @var \app\admin\model\system\AuthProject
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\system\AuthProject;
        $this->view->assign("statusList", $this->model->getStatusList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    ->with(['customer','admin'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                $row->visible(['auth_id','auth_name','status','start_date','end_date','createtime','updatetime']);
                $row->visible(['customer']);
				$row->getRelation('customer')->visible(['customer_name']);
				$row->visible(['admin']);
				$row->getRelation('admin')->visible(['nickname']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    

    /**
     * 添加
     *
     * @return string
     * @throws \think\Exception
     */
    public function add()
    {
        if (false === $this->request->isPost()) {
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);

        if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
            $params[$this->dataLimitField] = $this->auth->id;
        }
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                $this->model->validateFailException()->validate($validate);
            }
            $result = $this->model->allowField(true)->save($params);
            $auth_id = $this->model->auth_id;
            // 出货产品关联入库
            if($result){
                $outboundArr = json_decode($params['outbounds'], true);
                $insert = [];
                foreach ($outboundArr as $val) {
                    $insert[] = [
                        'auth_id' => $auth_id,
                        'outbound_id' => $val['id'],
                    ];
                }
                Db::name('auth_outbound')->insertAll($insert);
            }

            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        $this->success();
    }

    /**
     * 编辑
     *
     * @param $ids
     * @return string
     * @throws DbException
     * @throws \think\Exception
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }
        if (false === $this->request->isPost()) {
            $manager = \app\admin\model\system\Customer::where('customer_id', $row['customer_id'])->field('customer_owner, phone')->find();
            $row['customer_manager'] = $manager['customer_owner'];
            $row['phone'] = $manager['phone'];
            $outboundsList = Db::name('auth_outbound')->where('auth_id', $row['auth_id'])->select();
            $outboundsArr = [];
            foreach ($outboundsList as $outbound) {
                $outboundRow = \app\admin\model\system\Outbound::where('outbound_id', $outbound['outbound_id'])->find();
                $customer_name = \app\admin\model\system\Customer::where('customer_id', $row['customer_id'])->value('customer_name');
                $outboundsArr[] = [
                    'outbound_id' => $outbound['outbound_id'],
                    'project_name' => $outboundRow['project_name'],
                    'customer' => ['customer_name' => $customer_name],
                ];
            }
            $row['outbounds'] = $outboundsArr;
            $this->view->assign('row', $row);
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                $row->validateFailException()->validate($validate);
            }
            $result = $row->allowField(true)->save($params);
            // 授权项目出货关联入库
            if($result){
                // 删除原有授权项目出货关联记录
                Db::name('auth_outbound')->where('auth_id', $ids)->delete();
                // 插入新的出货产品记录
                $outboundArr = json_decode($params['outbounds'], true);
                $insert = [];
                foreach ($outboundArr as $val) {
                    $insert[] = [
                        'auth_id' => $ids,
                        'outbound_id' => $val['id'],
                    ];
                }
                Db::name('auth_outbound')->insertAll($insert);
            }
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success();
    }

    /**
     * 产品配置
     */
    public function product_config(){


        if (request()->isPost()) {
            
            $params = $this->request->post();

            //校验
            $validate = validate('app\admin\validate\system\AuthProjectData');

            if (!$validate->check($params['row'])) {
                $this->error($validate->getError());
            }

            foreach ($params['sub_products'] as $v) {
                if (empty($v['name'])) {
                    $this->error('产品：`' . $v['product_name'] . '`名称不能为空');
                }
                if (empty($v['module'])) {
                    $this->error('产品：`' . $v['product_name'] . '`所属模块不能为空');
                }
                if (empty($v['image'])) {
                    $this->error('产品：`' . $v['product_name'] . '`封面不能为空');
                }

                $r = Db::name('auth_project_list')->where('auth_id',$params['row']['auth_id'])->where('product_id',$v['product_id'])->find();

                $insert = [
                    'auth_id' => $params['row']['auth_id'],
                    'product_id' => $v['product_id'],
                    'name' => $v['name'],
                    'module' => $v['module'],
                    'image' => $v['image'],
                    'description' => $v['description'],
                    'weigh' => $v['weigh']
                ];

                if ($r) {
                    Db::name('auth_project_list')
                    ->where('auth_id',$params['row']['auth_id'])
                    ->where('product_id',$v['product_id'])
                    ->update($insert);
                } else {
                    Db::name('auth_project_list')
                    ->insert($insert);
                }

            }

            $model = model('app\admin\model\system\AuthProjectData');

            $row = $model->where('auth_id',$params['row']['auth_id'])->find();

            if ($row) {
                $result = $row->allowField(true)->save($params['row']);
            } else {
                $result = $model->allowField(true)->save($params['row']);
            }

            if (false === $result) {
                $this->error(__('No rows were updated'));
            }
            $this->success();

        }



        $model = model('app\admin\model\system\AuthProjectData');
        $auth_id = $this->request->param('auth_id');
        $row = $model->where('auth_id',$auth_id)->find();
        $outboundList = Db::name('auth_outbound')->where('auth_id', $auth_id)->select();
        $outboundPrduct = [];
        foreach($outboundList as $outbound){
            $outboundPrductRow = Db::name('outbound_product')->where('outbound_id', $outbound['outbound_id'])->column('product_id');
            $outboundPrduct = array_merge($outboundPrduct, $outboundPrductRow);
        }
        $product = \app\admin\model\system\Product::where('product_id', 'in', $outboundPrduct)->select();

        foreach ($product as &$v) {
            $data = Db::name('auth_project_list')->where('auth_id',$auth_id)->where('product_id',$v['product_id'])->find();
            if ($data) {
                $v['name'] = $data['name'];
                $v['module'] = $data['module'];
                $v['image'] = $data['image'];
                $v['weigh'] = $data['weigh'];
                $v['description'] = $data['description'];
            }
        }
        $this->view->assign('row', $row);
        $this->view->assign('auth_id', $auth_id);
        $this->view->assign('sub_products', $product);
        return $this->view->fetch();
    }

    /**
     * 授权
     */
    public function authorization(){
        if (false === $this->request->isPost()) {
            $auth_id = $this->request->param('auth_id');
            $customer_id = \app\admin\model\system\AuthProject::where('auth_id', $auth_id)->value('customer_id');
            $outboundList = Db::name('auth_outbound')->where('auth_id', $auth_id)->select();
            $outboundPrduct = [];
            foreach($outboundList as $outbound){
                $outboundPrductRow = Db::name('outbound_product')->where('outbound_id', $outbound['outbound_id'])->column('product_id');
                $outboundPrduct = array_merge($outboundPrduct, $outboundPrductRow);
            }
            $product = \app\admin\model\system\Product::where('product_id', 'in', $outboundPrduct)->select();
            foreach($product as &$v) {
                $v['platform_name'] = \app\admin\model\system\Platform::where('id', $v['platform_id'])->value('name');
                $row = Db::name('auth_project_authorization')->where(['auth_id'=>$auth_id,'product_id'=>$v['product_id']])->find();
                $visible_range = isset($row['visibility']) && $row['visibility'] == 2 ? '部分可见' : '默认全部';
                $v['visible_range'] = $visible_range;
                $v['customer_id'] = $customer_id;
                $v['visibility'] = $row['visibility'] ?? 1;
            }

            $this->view->assign('product', $product);
            $this->view->assign('auth_id', $auth_id);
            return $this->view->fetch();
        }else{
            $data = $this->request->post();
            $insert = [];
            if(!empty($data['users']))
            {
                foreach($data['users'] as $key=>$item){
                    Db::name('auth_project_authorization')->where(['auth_id'=>$data['auth_id'],'product_id'=>$key])->delete();
                    $insert[] = [
                        'auth_id' => $data['auth_id'],
                        'product_id'=> $key,
                        'visibility' => ($item['visibility'] == 1 ? 1 : 2),
                        'user_ids' => ($item['visibility'] == 2 ? implode(',', $item['users']) : '')
                    ];
                }
                $result = Db::name('auth_project_authorization')->insertAll($insert);
                
                if($result){
                    try {
                        $syncResults = $this->model->syncAuthorization($data['auth_id']);
                        $msg = "授权成功，同步数据如下：\n";
                        foreach($syncResults as $result) {
                            if (isset($result['error'])) {
                                $msg .= $result['platform'] . ": 同步失败 - " . $result['error'] . "\n";
                            } else {
                                $msg .= $result['platform'] . ": " . $result['num'] . "条数据\n";
                            }
                        }
                        $this->success($msg);
                    } catch (\Exception $e) {
                        \think\Log::error("授权同步异常", ['auth_id' => $data['auth_id'], 'error' => $e->getMessage()]);
                        $this->error('授权成功，但同步数据失败：' . $e->getMessage());
                    }
                }else{
                    $this->error('授权失败');
                }
            }else{
                $this->success('无更新内容');
            }
        }
    }

    /**
     * test
     */
    public function test(){
        $syncResults = $this->model->syncAuthorization(3);
        dump($syncResults);exit;
        $msg = "测试同步结果：\n";
        foreach($syncResults as $result){
            if (isset($result['error'])) {
                $msg .= $result['platform'] . ": 同步失败 - " . $result['error'] . "\n";
            } else {
                $msg .= $result['platform'] . ": " . $result['num'] . "条数据\n";
            }
        }
        dump($msg);
    }

}
