<?php

namespace app\admin\controller\system;

use app\common\controller\Backend;
use think\Db;

/**
 * 产品管理
 *
 * @icon fa fa-circle-o
 */
class Product extends Backend
{

    /**
     * Product模型对象
     * @var \app\admin\model\system\Product
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\system\Product;

    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    ->with(['platform','customer'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                $row->visible(['product_id','product_name','createtime','updatetime']);
                $row->visible(['platform']);
				$row->getRelation('platform')->visible(['name']);
				$row->visible(['customer']);
				$row->getRelation('customer')->visible(['customer_name']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 同步子系统产品信息
     */
    public function sync()
    {
        # 从Training平台获取客户信息
        $insertCustomer = [];
        $customerList = Db::connect('vtrs')->name('user_school')->field('id,name')->select();
        foreach($customerList as $k => $v){
            $data = [
                'customer_name' => $v['name'],
                'createtime' => time(),
                'updatetime' => time(),
            ];
            
            // 检查是否已存在该产品记录
            $exist = \app\admin\model\system\Customer::where('customer_name', $v['name'])->find();
            
            if ($exist) {
                continue;
            } else {
                // 不存在则新增
                $insertCustomer[] = $data;
            }
        }
        // 批量插入新数据
        if (!empty($insertCustomer)) {
            \app\admin\model\system\Customer::insertAll($insertCustomer);
        }

        $platform = \app\admin\model\system\Platform::select();
        $insert = [];
        $update = [];
        
        foreach ($platform as $key => $value) {
            $productList = [];
            
            switch ($value->config_name) {
                case 'aimaster':
                    $productList = Db::connect('aimaster')->name('ai_project')->field('id,name')->select();
                    break;
                
                case 'vtrs':
                    $productList = Db::connect('vtrs')->name('product')->field('id,name')->select();
                    break;
                    
                case 'formmaster':
                    $productList = Db::connect('formmaster')->name('from_project')->field('id,name')->select();
                    break;
                    
                case 'examadmin':
                    $productList = Db::connect('examadmin')->name('project')->field('id,name')->select();
                    break;
                case 'case':
                    $productList = Db::connect('vtrs')->name('user_case_system')->field('id,name')->select();
                    break;
                
                default:
                    // 其他平台暂不处理
                    break;
            }
            
            // 处理从各平台获取的产品数据
            foreach($productList as $k => $v){
                // $customer_id = \app\admin\model\system\Customer::where('name', $v['name'])->value('id') ?? '';
                $data = [
                    'product_name' => $v['name'],
                    'subsystem_id' => $v['id'],
                    'platform_id' => $value->id,
                    'customer_id' => 0,
                    'createtime' => time(),
                    'updatetime' => time(),
                ];
                
                // 检查是否已存在该产品记录
                $exist = $this->model->where([
                    'subsystem_id' => $v['id'],
                    'platform_id' => $value->id
                ])->find();
                
                if ($exist) {
                    // 如果存在则更新
                    $update[] = $exist->product_id;
                    $this->model->where([
                        'product_id' => $v['id'],
                        'platform_id' => $value->id
                    ])->update($data);
                } else {
                    // 不存在则新增
                    $insert[] = $data;
                }
            }
        }
        
        // 批量插入新数据
        if (!empty($insert)) {
            $this->model->insertAll($insert);
        }
        
        return json(['code' => 1, 'msg' => '同步完成，新增' . count($insert) . '条，更新' . count($update) . '条']);
    }

    /**
     * 选择子产品页面
     */
    public function select(){
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    ->with(['platform','customer'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                $row->visible(['product_id','product_name','createtime','updatetime']);
                $row->visible(['platform']);
				$row->getRelation('platform')->visible(['name']);
				$row->visible(['customer']);
				$row->getRelation('customer')->visible(['customer_name']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

}
